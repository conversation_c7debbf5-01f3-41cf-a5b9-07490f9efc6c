# -*- coding: utf-8 -*-
"""
章节编辑页面
提供章节内容的编辑和写作功能
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from typing import Dict, Any

from config.ui_config import UIConfig
from utils.ui_utils import UIUtils
from utils.logger import get_logger
from utils.text_processor import TextProcessor

class ChapterEditingPage(QWidget):
    """章节编辑页面"""
    
    def __init__(self):
        super().__init__()
        self.ui_config = UIConfig()
        self.logger = get_logger()
        self.text_processor = TextProcessor()
        
        # 当前章节数据
        self.current_chapter_data = {}
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self.auto_save)
        
        # 初始化UI
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(16)
        
        # 页面标题和工具栏
        header_layout = self.create_header()
        layout.addLayout(header_layout)
        
        # 主要内容区域
        content_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：章节列表
        left_panel = self.create_chapter_list_panel()
        content_splitter.addWidget(left_panel)
        
        # 中间：编辑器
        center_panel = self.create_editor_panel()
        content_splitter.addWidget(center_panel)
        
        # 右侧：工具面板
        right_panel = self.create_tools_panel()
        content_splitter.addWidget(right_panel)
        
        # 设置分割比例
        content_splitter.setStretchFactor(0, 1)
        content_splitter.setStretchFactor(1, 3)
        content_splitter.setStretchFactor(2, 1)
        
        layout.addWidget(content_splitter)
        
    def create_header(self) -> QHBoxLayout:
        """创建页面头部"""
        header_layout = QHBoxLayout()
        
        # 页面标题
        title_label = QLabel("📄 章节编辑")
        title_label.setFont(self.ui_config.get_font("title"))
        title_label.setStyleSheet(f"color: {self.ui_config.colors.TEXT_PRIMARY}; font-weight: bold;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # 工具按钮
        self.save_btn = UIUtils.create_material_button("💾 保存", button_type="primary")
        self.save_btn.clicked.connect(self.save_chapter)
        header_layout.addWidget(self.save_btn)
        
        self.auto_save_checkbox = QCheckBox("自动保存")
        self.auto_save_checkbox.setChecked(True)
        self.auto_save_checkbox.toggled.connect(self.toggle_auto_save)
        header_layout.addWidget(self.auto_save_checkbox)
        
        return header_layout
        
    def create_chapter_list_panel(self) -> QWidget:
        """创建章节列表面板"""
        panel = UIUtils.create_card_widget("章节列表")
        layout = panel.layout()
        
        # 搜索框
        self.search_input = UIUtils.create_material_input("搜索章节...")
        self.search_input.textChanged.connect(self.filter_chapters)
        layout.addWidget(self.search_input)
        
        # 章节列表
        self.chapter_list = QListWidget()
        self.chapter_list.itemClicked.connect(self.on_chapter_selected)
        layout.addWidget(self.chapter_list)
        
        # 加载示例章节
        self.load_sample_chapters()
        
        return panel
        
    def create_editor_panel(self) -> QWidget:
        """创建编辑器面板"""
        panel = UIUtils.create_card_widget()
        layout = panel.layout()
        
        # 章节信息栏
        info_layout = QHBoxLayout()
        
        self.chapter_title_label = QLabel("未选择章节")
        self.chapter_title_label.setFont(self.ui_config.get_font("subtitle"))
        info_layout.addWidget(self.chapter_title_label)
        
        info_layout.addStretch()
        
        self.word_count_label = QLabel("字数: 0")
        info_layout.addWidget(self.word_count_label)
        
        self.target_words_label = QLabel("目标: 0")
        info_layout.addWidget(self.target_words_label)
        
        layout.addLayout(info_layout)
        
        # 编辑器工具栏
        toolbar_layout = QHBoxLayout()
        
        self.bold_btn = UIUtils.create_material_button("B", button_type="outline")
        self.bold_btn.setMaximumWidth(40)
        self.bold_btn.clicked.connect(self.format_bold)
        toolbar_layout.addWidget(self.bold_btn)
        
        self.italic_btn = UIUtils.create_material_button("I", button_type="outline")
        self.italic_btn.setMaximumWidth(40)
        self.italic_btn.clicked.connect(self.format_italic)
        toolbar_layout.addWidget(self.italic_btn)
        
        toolbar_layout.addWidget(UIUtils.create_separator(Qt.Vertical))
        
        self.font_size_combo = UIUtils.create_material_combobox(["12", "14", "16", "18", "20"])
        self.font_size_combo.setCurrentText("14")
        self.font_size_combo.currentTextChanged.connect(self.change_font_size)
        toolbar_layout.addWidget(self.font_size_combo)
        
        toolbar_layout.addStretch()
        
        self.ai_assist_btn = UIUtils.create_material_button("🤖 AI助手", button_type="secondary")
        self.ai_assist_btn.clicked.connect(self.show_ai_assistant)
        toolbar_layout.addWidget(self.ai_assist_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 文本编辑器
        self.text_editor = QTextEdit()
        self.text_editor.setPlaceholderText("开始写作...")
        self.text_editor.textChanged.connect(self.on_text_changed)
        
        # 设置编辑器样式
        editor_style = f"""
            QTextEdit {{
                border: 2px solid {self.ui_config.colors.OUTLINE};
                border-radius: 8px;
                padding: 16px;
                background-color: {self.ui_config.colors.SURFACE};
                color: {self.ui_config.colors.TEXT_PRIMARY};
                font-family: 'Microsoft YaHei';
                font-size: 14px;
                line-height: 1.6;
            }}
            QTextEdit:focus {{
                border-color: {self.ui_config.colors.PRIMARY};
            }}
        """
        self.text_editor.setStyleSheet(editor_style)
        
        layout.addWidget(self.text_editor)
        
        return panel
        
    def create_tools_panel(self) -> QWidget:
        """创建工具面板"""
        panel = UIUtils.create_card_widget("写作工具")
        layout = panel.layout()
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 大纲标签页
        outline_tab = self.create_outline_tab()
        tab_widget.addTab(outline_tab, "📋 大纲")
        
        # 统计标签页
        stats_tab = self.create_stats_tab()
        tab_widget.addTab(stats_tab, "📊 统计")
        
        # 人物标签页
        characters_tab = self.create_characters_tab()
        tab_widget.addTab(characters_tab, "👥 人物")
        
        layout.addWidget(tab_widget)
        
        return panel
        
    def create_outline_tab(self) -> QWidget:
        """创建大纲标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 章节大纲
        outline_label = QLabel("章节大纲:")
        layout.addWidget(outline_label)
        
        self.chapter_outline_text = QTextEdit()
        self.chapter_outline_text.setMaximumHeight(120)
        self.chapter_outline_text.setPlaceholderText("章节大纲...")
        layout.addWidget(self.chapter_outline_text)
        
        # 情节点
        plot_label = QLabel("关键情节:")
        layout.addWidget(plot_label)
        
        self.plot_points_list = QListWidget()
        self.plot_points_list.setMaximumHeight(100)
        layout.addWidget(self.plot_points_list)
        
        # 添加情节点按钮
        add_plot_btn = UIUtils.create_material_button("+ 添加情节点", button_type="outline")
        add_plot_btn.clicked.connect(self.add_plot_point)
        layout.addWidget(add_plot_btn)
        
        layout.addStretch()
        return tab
        
    def create_stats_tab(self) -> QWidget:
        """创建统计标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 字数统计
        stats_layout = QFormLayout()
        
        self.current_words_label = QLabel("0")
        stats_layout.addRow("当前字数:", self.current_words_label)
        
        self.target_words_stat_label = QLabel("0")
        stats_layout.addRow("目标字数:", self.target_words_stat_label)
        
        self.progress_label = QLabel("0%")
        stats_layout.addRow("完成进度:", self.progress_label)
        
        self.paragraphs_label = QLabel("0")
        stats_layout.addRow("段落数:", self.paragraphs_label)
        
        self.sentences_label = QLabel("0")
        stats_layout.addRow("句子数:", self.sentences_label)
        
        layout.addLayout(stats_layout)
        
        # 进度条
        self.progress_bar = UIUtils.create_progress_bar()
        layout.addWidget(self.progress_bar)
        
        # 内容分析
        analysis_label = QLabel("内容分析:")
        analysis_label.setFont(self.ui_config.get_font("subtitle"))
        layout.addWidget(analysis_label)
        
        self.content_analysis_text = QTextEdit()
        self.content_analysis_text.setMaximumHeight(100)
        self.content_analysis_text.setReadOnly(True)
        layout.addWidget(self.content_analysis_text)
        
        # 分析按钮
        analyze_btn = UIUtils.create_material_button("📊 分析内容", button_type="outline")
        analyze_btn.clicked.connect(self.analyze_content)
        layout.addWidget(analyze_btn)
        
        layout.addStretch()
        return tab
        
    def create_characters_tab(self) -> QWidget:
        """创建人物标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 出场人物
        characters_label = QLabel("出场人物:")
        layout.addWidget(characters_label)
        
        self.characters_list = QListWidget()
        self.characters_list.setMaximumHeight(120)
        layout.addWidget(self.characters_list)
        
        # 添加人物按钮
        add_character_btn = UIUtils.create_material_button("+ 添加人物", button_type="outline")
        add_character_btn.clicked.connect(self.add_character)
        layout.addWidget(add_character_btn)
        
        # 人物关系
        relations_label = QLabel("人物关系:")
        layout.addWidget(relations_label)
        
        self.relations_text = QTextEdit()
        self.relations_text.setMaximumHeight(80)
        self.relations_text.setPlaceholderText("人物关系描述...")
        layout.addWidget(self.relations_text)
        
        layout.addStretch()
        return tab
        
    def load_sample_chapters(self):
        """加载示例章节"""
        sample_chapters = [
            "第1章 初入江湖",
            "第2章 奇遇良师", 
            "第3章 武功初成",
            "第4章 江湖恩怨",
            "第5章 生死考验"
        ]
        
        for chapter in sample_chapters:
            item = QListWidgetItem(chapter)
            item.setData(Qt.UserRole, {
                "title": chapter,
                "content": "",
                "outline": f"{chapter}的大纲内容...",
                "target_words": 3000,
                "status": "未开始"
            })
            self.chapter_list.addItem(item)
            
    def on_chapter_selected(self, item: QListWidgetItem):
        """章节选中事件"""
        chapter_data = item.data(Qt.UserRole)
        if chapter_data:
            self.load_chapter(chapter_data)
            
    def load_chapter(self, chapter_data: Dict[str, Any]):
        """加载章节"""
        self.current_chapter_data = chapter_data
        
        # 更新章节信息
        self.chapter_title_label.setText(chapter_data.get("title", "未命名章节"))
        self.target_words_label.setText(f"目标: {chapter_data.get('target_words', 0)}")
        
        # 加载内容
        self.text_editor.setPlainText(chapter_data.get("content", ""))
        self.chapter_outline_text.setPlainText(chapter_data.get("outline", ""))
        
        # 更新统计
        self.update_statistics()
        
        self.logger.info(f"加载章节: {chapter_data.get('title')}")
        
    def on_text_changed(self):
        """文本变化事件"""
        self.update_statistics()
        
        # 启动自动保存定时器
        if self.auto_save_checkbox.isChecked():
            self.auto_save_timer.start(5000)  # 5秒后自动保存
            
    def update_statistics(self):
        """更新统计信息"""
        content = self.text_editor.toPlainText()
        
        # 字数统计
        word_count = self.text_processor.count_words(content)
        paragraph_count = self.text_processor.count_paragraphs(content)
        sentence_count = self.text_processor.count_sentences(content)
        
        self.word_count_label.setText(f"字数: {word_count:,}")
        self.current_words_label.setText(str(word_count))
        self.paragraphs_label.setText(str(paragraph_count))
        self.sentences_label.setText(str(sentence_count))
        
        # 进度计算
        target_words = self.current_chapter_data.get("target_words", 0)
        if target_words > 0:
            progress = min(100, int(word_count / target_words * 100))
            self.progress_label.setText(f"{progress}%")
            self.progress_bar.setValue(progress)
            self.target_words_stat_label.setText(str(target_words))
        else:
            self.progress_label.setText("0%")
            self.progress_bar.setValue(0)
            
    def analyze_content(self):
        """分析内容"""
        content = self.text_editor.toPlainText()
        if not content.strip():
            self.content_analysis_text.setPlainText("暂无内容可分析")
            return
            
        # 内容类型分析
        content_types = self.text_processor.analyze_content_type(content)
        
        # AI味检测
        ai_analysis = self.text_processor.detect_ai_patterns(content)
        
        # 生成分析报告
        analysis_text = f"""内容类型分布:
叙述: {content_types['叙述']}%
对话: {content_types['对话']}%
描写: {content_types['描写']}%
心理: {content_types['心理']}%

AI味评分: {ai_analysis['ai_score']}/100
"""
        
        if ai_analysis['suggestions']:
            analysis_text += "\n改进建议:\n"
            for suggestion in ai_analysis['suggestions'][:3]:
                analysis_text += f"• {suggestion}\n"
                
        self.content_analysis_text.setPlainText(analysis_text)
        
    def save_chapter(self):
        """保存章节"""
        if not self.current_chapter_data:
            UIUtils.show_message(self, "提示", "请先选择要保存的章节", "info")
            return
            
        # 更新章节数据
        self.current_chapter_data["content"] = self.text_editor.toPlainText()
        self.current_chapter_data["outline"] = self.chapter_outline_text.toPlainText()
        
        # 更新列表项数据
        current_item = self.chapter_list.currentItem()
        if current_item:
            current_item.setData(Qt.UserRole, self.current_chapter_data)
            
        self.logger.info(f"保存章节: {self.current_chapter_data.get('title')}")
        UIUtils.show_message(self, "保存成功", "章节已保存", "info")
        
    def auto_save(self):
        """自动保存"""
        self.auto_save_timer.stop()
        if self.current_chapter_data:
            self.current_chapter_data["content"] = self.text_editor.toPlainText()
            self.logger.info("自动保存章节")
            
    def toggle_auto_save(self, enabled: bool):
        """切换自动保存"""
        if not enabled:
            self.auto_save_timer.stop()
            
    def filter_chapters(self, text: str):
        """过滤章节"""
        for i in range(self.chapter_list.count()):
            item = self.chapter_list.item(i)
            chapter_data = item.data(Qt.UserRole)
            title = chapter_data.get("title", "")
            item.setHidden(text.lower() not in title.lower())
            
    def format_bold(self):
        """加粗格式"""
        cursor = self.text_editor.textCursor()
        if cursor.hasSelection():
            format = QTextCharFormat()
            format.setFontWeight(QFont.Bold)
            cursor.mergeCharFormat(format)
            
    def format_italic(self):
        """斜体格式"""
        cursor = self.text_editor.textCursor()
        if cursor.hasSelection():
            format = QTextCharFormat()
            format.setFontItalic(True)
            cursor.mergeCharFormat(format)
            
    def change_font_size(self, size: str):
        """改变字体大小"""
        try:
            font_size = int(size)
            font = self.text_editor.font()
            font.setPointSize(font_size)
            self.text_editor.setFont(font)
        except ValueError:
            pass
            
    def show_ai_assistant(self):
        """显示AI助手"""
        self.logger.info("显示AI助手")
        # TODO: 实现AI助手对话框
        
    def add_plot_point(self):
        """添加情节点"""
        text, ok = QInputDialog.getText(self, "添加情节点", "请输入情节点:")
        if ok and text.strip():
            self.plot_points_list.addItem(text.strip())
            
    def add_character(self):
        """添加人物"""
        text, ok = QInputDialog.getText(self, "添加人物", "请输入人物名称:")
        if ok and text.strip():
            self.characters_list.addItem(text.strip())
