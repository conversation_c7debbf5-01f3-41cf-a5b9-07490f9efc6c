#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI小说助手安装脚本
自动安装依赖包和初始化环境
"""

import sys
import subprocess
import os
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"✓ Python版本检查通过: {sys.version}")
    return True

def install_requirements():
    """安装依赖包"""
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        print("错误: 找不到requirements.txt文件")
        return False
    
    print("正在安装依赖包...")
    
    try:
        # 升级pip
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # 安装依赖
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("✓ 依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"错误: 安装依赖包失败 - {e}")
        return False

def create_directories():
    """创建必要的目录"""
    directories = [
        "logs",
        "projects", 
        "backups",
        "data/icons",
        "data/templates",
        "data/prompts"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✓ 目录结构创建完成")

def create_desktop_shortcut():
    """创建桌面快捷方式（Windows）"""
    if sys.platform != "win32":
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "AI小说助手.lnk")
        target = os.path.join(os.getcwd(), "run.py")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print("✓ 桌面快捷方式创建完成")
        
    except ImportError:
        print("提示: 如需创建桌面快捷方式，请安装pywin32包")
    except Exception as e:
        print(f"警告: 创建桌面快捷方式失败 - {e}")

def test_installation():
    """测试安装"""
    print("正在测试安装...")
    
    try:
        # 测试导入主要模块
        import PySide6
        import jieba
        import cryptography
        
        print("✓ 核心模块导入测试通过")
        
        # 测试应用程序导入
        from config.settings import AppSettings
        from utils.logger import setup_logger
        
        print("✓ 应用程序模块导入测试通过")
        return True
        
    except ImportError as e:
        print(f"错误: 模块导入失败 - {e}")
        return False

def main():
    """主安装流程"""
    print("=" * 50)
    print("AI小说助手 - 安装程序")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装依赖包
    if not install_requirements():
        sys.exit(1)
    
    # 创建目录
    create_directories()
    
    # 创建快捷方式
    create_desktop_shortcut()
    
    # 测试安装
    if not test_installation():
        print("安装测试失败，请检查错误信息")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("✓ 安装完成！")
    print("=" * 50)
    print("使用方法:")
    print("1. 运行: python run.py")
    print("2. 或双击桌面快捷方式（Windows）")
    print("\n首次使用请配置AI API密钥")
    print("=" * 50)

if __name__ == "__main__":
    main()
