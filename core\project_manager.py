# -*- coding: utf-8 -*-
"""
项目管理器
负责项目的创建、保存、加载等操作
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

from utils.file_handler import ProjectFileManager
from utils.logger import get_logger, ErrorHandler

class ProjectManager:
    """项目管理器"""
    
    def __init__(self):
        self.file_manager = ProjectFileManager()
        self.logger = get_logger()
        self.error_handler = ErrorHandler()
        
        # 当前项目数据
        self.current_project: Optional[Dict[str, Any]] = None
        self.current_file_path: Optional[str] = None
        self.is_modified = False
        
    def create_new_project(self, basic_info: Dict[str, Any]) -> Dict[str, Any]:
        """创建新项目"""
        try:
            project_data = {
                "basic_info": {
                    "title": basic_info.get("title", "未命名项目"),
                    "genre": basic_info.get("genre", ""),
                    "theme": basic_info.get("theme", ""),
                    "style": basic_info.get("style", ""),
                    "word_count": basic_info.get("word_count", ""),
                    "chapter_count": basic_info.get("chapter_count", ""),
                    "background": basic_info.get("background", ""),
                    "characters": basic_info.get("characters", ""),
                    "conflict": basic_info.get("conflict", "")
                },
                "outline": {
                    "summary": "",
                    "volumes": [],
                    "chapters": [],
                    "plot_lines": [],
                    "key_events": []
                },
                "characters": [],
                "chapters": [],
                "settings": {
                    "auto_save": True,
                    "backup_enabled": True,
                    "vector_db_enabled": True
                },
                "statistics": {
                    "total_words": 0,
                    "completed_chapters": 0,
                    "creation_date": datetime.now().isoformat(),
                    "last_modified": datetime.now().isoformat()
                }
            }
            
            self.current_project = project_data
            self.current_file_path = None
            self.is_modified = True
            
            self.logger.info(f"创建新项目: {basic_info.get('title')}")
            return project_data
            
        except Exception as e:
            error_msg = self.error_handler.handle_unknown_error(e, "创建项目")
            raise Exception(error_msg)
            
    def save_project(self, file_path: str = None) -> bool:
        """保存项目"""
        try:
            if not self.current_project:
                raise Exception("没有可保存的项目")
                
            # 更新修改时间
            self.current_project["statistics"]["last_modified"] = datetime.now().isoformat()
            
            # 确定保存路径
            save_path = file_path or self.current_file_path
            if not save_path:
                raise Exception("请指定保存路径")
                
            # 保存项目
            self.file_manager.save_project(self.current_project, save_path)
            
            self.current_file_path = save_path
            self.is_modified = False
            
            self.logger.info(f"保存项目: {save_path}")
            return True
            
        except Exception as e:
            error_msg = self.error_handler.handle_file_error(e, file_path or "")
            raise Exception(error_msg)
            
    def load_project(self, file_path: str) -> Dict[str, Any]:
        """加载项目"""
        try:
            project_data = self.file_manager.load_project(file_path)
            
            self.current_project = project_data
            self.current_file_path = file_path
            self.is_modified = False
            
            self.logger.info(f"加载项目: {file_path}")
            return project_data
            
        except Exception as e:
            error_msg = self.error_handler.handle_file_error(e, file_path)
            raise Exception(error_msg)
            
    def export_project_text(self, output_path: str) -> bool:
        """导出项目为文本"""
        try:
            if not self.current_project:
                raise Exception("没有可导出的项目")
                
            success = self.file_manager.export_project_text(self.current_project, output_path)
            
            if success:
                self.logger.info(f"导出项目文本: {output_path}")
            
            return success
            
        except Exception as e:
            error_msg = self.error_handler.handle_file_error(e, output_path)
            raise Exception(error_msg)
            
    def create_backup(self) -> str:
        """创建备份"""
        try:
            if not self.current_project:
                raise Exception("没有可备份的项目")
                
            backup_path = self.file_manager.create_backup(self.current_project)
            self.logger.info(f"创建备份: {backup_path}")
            return backup_path
            
        except Exception as e:
            error_msg = self.error_handler.handle_file_error(e)
            raise Exception(error_msg)
            
    def get_recent_projects(self, max_count: int = 10) -> List[Dict[str, Any]]:
        """获取最近项目"""
        try:
            return self.file_manager.get_recent_projects(max_count)
        except Exception as e:
            self.logger.error(f"获取最近项目失败: {e}")
            return []
            
    def update_outline(self, outline_data: Dict[str, Any]):
        """更新大纲"""
        if not self.current_project:
            raise Exception("没有当前项目")
            
        self.current_project["outline"].update(outline_data)
        self.is_modified = True
        self.logger.info("更新项目大纲")
        
    def add_character(self, character_data: Dict[str, Any]):
        """添加角色"""
        if not self.current_project:
            raise Exception("没有当前项目")
            
        self.current_project["characters"].append(character_data)
        self.is_modified = True
        self.logger.info(f"添加角色: {character_data.get('name')}")
        
    def update_character(self, character_id: str, character_data: Dict[str, Any]):
        """更新角色"""
        if not self.current_project:
            raise Exception("没有当前项目")
            
        for i, char in enumerate(self.current_project["characters"]):
            if char.get("id") == character_id:
                self.current_project["characters"][i] = character_data
                self.is_modified = True
                self.logger.info(f"更新角色: {character_data.get('name')}")
                return
                
        raise Exception(f"未找到角色: {character_id}")
        
    def add_chapter(self, chapter_data: Dict[str, Any]):
        """添加章节"""
        if not self.current_project:
            raise Exception("没有当前项目")
            
        # 生成章节ID
        chapter_data["id"] = f"chapter_{len(self.current_project['chapters']) + 1}"
        chapter_data["created_date"] = datetime.now().isoformat()
        chapter_data["modified_date"] = datetime.now().isoformat()
        
        self.current_project["chapters"].append(chapter_data)
        self.is_modified = True
        self.logger.info(f"添加章节: {chapter_data.get('title')}")
        
    def update_chapter(self, chapter_id: str, chapter_data: Dict[str, Any]):
        """更新章节"""
        if not self.current_project:
            raise Exception("没有当前项目")
            
        for i, chapter in enumerate(self.current_project["chapters"]):
            if chapter.get("id") == chapter_id:
                chapter_data["modified_date"] = datetime.now().isoformat()
                self.current_project["chapters"][i] = chapter_data
                self.is_modified = True
                self.logger.info(f"更新章节: {chapter_data.get('title')}")
                return
                
        raise Exception(f"未找到章节: {chapter_id}")
        
    def delete_chapter(self, chapter_id: str):
        """删除章节"""
        if not self.current_project:
            raise Exception("没有当前项目")
            
        for i, chapter in enumerate(self.current_project["chapters"]):
            if chapter.get("id") == chapter_id:
                del self.current_project["chapters"][i]
                self.is_modified = True
                self.logger.info(f"删除章节: {chapter.get('title')}")
                return
                
        raise Exception(f"未找到章节: {chapter_id}")
        
    def get_project_statistics(self) -> Dict[str, Any]:
        """获取项目统计"""
        if not self.current_project:
            return {}
            
        chapters = self.current_project.get("chapters", [])
        
        total_words = sum(len(ch.get("content", "")) for ch in chapters)
        completed_chapters = sum(1 for ch in chapters if ch.get("status") == "已完成")
        
        return {
            "total_chapters": len(chapters),
            "completed_chapters": completed_chapters,
            "total_words": total_words,
            "completion_rate": completed_chapters / len(chapters) * 100 if chapters else 0,
            "average_words_per_chapter": total_words / len(chapters) if chapters else 0
        }
        
    def search_content(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索内容"""
        if not self.current_project:
            return []
            
        results = []
        
        # 搜索章节内容
        for chapter in self.current_project.get("chapters", []):
            content = chapter.get("content", "")
            title = chapter.get("title", "")
            
            if keyword.lower() in content.lower() or keyword.lower() in title.lower():
                results.append({
                    "type": "chapter",
                    "id": chapter.get("id"),
                    "title": title,
                    "content_preview": content[:100] + "..." if len(content) > 100 else content
                })
                
        # 搜索角色信息
        for character in self.current_project.get("characters", []):
            name = character.get("name", "")
            description = character.get("description", "")
            
            if keyword.lower() in name.lower() or keyword.lower() in description.lower():
                results.append({
                    "type": "character",
                    "id": character.get("id"),
                    "name": name,
                    "description_preview": description[:100] + "..." if len(description) > 100 else description
                })
                
        return results
        
    def get_current_project(self) -> Optional[Dict[str, Any]]:
        """获取当前项目"""
        return self.current_project
        
    def is_project_modified(self) -> bool:
        """检查项目是否已修改"""
        return self.is_modified
        
    def mark_as_modified(self):
        """标记为已修改"""
        self.is_modified = True
        
    def close_project(self):
        """关闭项目"""
        self.current_project = None
        self.current_file_path = None
        self.is_modified = False
        self.logger.info("关闭项目")
