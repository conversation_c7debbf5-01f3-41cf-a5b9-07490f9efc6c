#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI小说助手测试脚本
测试核心功能是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        # 测试配置模块
        from config.settings import AppSettings
        from config.api_config import APIConfig
        from config.ui_config import UIConfig
        print("✓ 配置模块导入成功")
        
        # 测试工具模块
        from utils.logger import setup_logger
        from utils.file_handler import FileHandler
        from utils.text_processor import TextProcessor
        from utils.ui_utils import UIUtils
        print("✓ 工具模块导入成功")
        
        # 测试核心模块
        from core.ai_manager import AIManager
        from core.project_manager import ProjectManager
        print("✓ 核心模块导入成功")
        
        # 测试UI模块
        from ui.main_window import MainWindow
        print("✓ UI模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        return False

def test_config():
    """测试配置功能"""
    print("\n测试配置功能...")
    
    try:
        from config.settings import AppSettings
        
        # 测试设置管理
        settings = AppSettings()
        
        # 测试获取配置
        language = settings.get("app.language", "zh_CN")
        print(f"✓ 语言设置: {language}")
        
        # 测试设置配置
        settings.set("test.value", "test_data")
        value = settings.get("test.value")
        assert value == "test_data"
        print("✓ 配置读写测试通过")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def test_logger():
    """测试日志功能"""
    print("\n测试日志功能...")
    
    try:
        from utils.logger import setup_logger
        
        # 设置日志
        logger = setup_logger("测试日志", "test_logs")
        
        # 测试日志记录
        logger.info("这是一条测试信息")
        logger.warning("这是一条测试警告")
        logger.error("这是一条测试错误")
        
        print("✓ 日志功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 日志测试失败: {e}")
        return False

def test_text_processor():
    """测试文本处理功能"""
    print("\n测试文本处理功能...")
    
    try:
        from utils.text_processor import TextProcessor
        
        processor = TextProcessor()
        
        # 测试文本
        test_text = """
        这是一个测试文本。包含了多个句子！
        还有第二段内容？用来测试各种功能。
        "这里有对话内容。"他说道。
        """
        
        # 测试字数统计
        word_count = processor.count_words(test_text)
        print(f"✓ 字数统计: {word_count}")
        
        # 测试段落统计
        paragraph_count = processor.count_paragraphs(test_text)
        print(f"✓ 段落统计: {paragraph_count}")
        
        # 测试句子统计
        sentence_count = processor.count_sentences(test_text)
        print(f"✓ 句子统计: {sentence_count}")
        
        # 测试内容分析
        content_types = processor.analyze_content_type(test_text)
        print(f"✓ 内容分析: {content_types}")
        
        return True
        
    except Exception as e:
        print(f"✗ 文本处理测试失败: {e}")
        return False

def test_project_manager():
    """测试项目管理功能"""
    print("\n测试项目管理功能...")
    
    try:
        from core.project_manager import ProjectManager
        
        manager = ProjectManager()
        
        # 测试创建项目
        basic_info = {
            "title": "测试小说",
            "genre": "都市言情",
            "theme": "成长",
            "style": "轻松幽默"
        }
        
        project = manager.create_new_project(basic_info)
        print(f"✓ 创建项目: {project['basic_info']['title']}")
        
        # 测试添加章节
        chapter_data = {
            "title": "第一章 开始",
            "outline": "主角登场",
            "content": "这是第一章的内容...",
            "status": "已完成"
        }
        
        manager.add_chapter(chapter_data)
        print("✓ 添加章节成功")
        
        # 测试统计信息
        stats = manager.get_project_statistics()
        print(f"✓ 项目统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"✗ 项目管理测试失败: {e}")
        return False

def test_ai_manager():
    """测试AI管理功能"""
    print("\n测试AI管理功能...")
    
    try:
        from core.ai_manager import AIManager
        
        manager = AIManager()
        
        # 测试获取可用提供商
        providers = manager.get_available_providers()
        print(f"✓ 可用AI提供商: {len(providers)}个")
        
        # 测试提供商连接（模拟）
        if providers:
            test_result = manager.test_provider(providers[0])
            print(f"✓ 提供商连接测试: {'成功' if test_result else '失败'}")
        
        return True
        
    except Exception as e:
        print(f"✗ AI管理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("AI小说助手 - 功能测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config,
        test_logger,
        test_text_processor,
        test_project_manager,
        test_ai_manager
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 50)
    
    if passed == total:
        print("✓ 所有测试通过！应用程序可以正常运行。")
        return 0
    else:
        print("✗ 部分测试失败，请检查错误信息。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
