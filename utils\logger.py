# -*- coding: utf-8 -*-
"""
日志管理模块
提供统一的日志记录功能，支持中文显示
"""

import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from logging.handlers import RotatingFileHandler
from typing import Optional

class ChineseFormatter(logging.Formatter):
    """中文日志格式化器"""
    
    def __init__(self, fmt=None, datefmt=None):
        # 中文日志级别映射
        self.level_names = {
            'DEBUG': '调试',
            'INFO': '信息', 
            'WARNING': '警告',
            'ERROR': '错误',
            'CRITICAL': '严重错误'
        }
        super().__init__(fmt, datefmt)
        
    def format(self, record):
        # 替换日志级别为中文
        original_levelname = record.levelname
        record.levelname = self.level_names.get(original_levelname, original_levelname)
        
        # 格式化记录
        formatted = super().format(record)
        
        # 恢复原始级别名
        record.levelname = original_levelname
        
        return formatted

class Logger:
    """日志管理器"""
    
    def __init__(self, name: str = "AI小说助手", log_dir: str = "logs"):
        self.name = name
        self.log_dir = Path(log_dir)
        self.logger = None
        self._setup_logger()
        
    def _setup_logger(self):
        """设置日志器"""
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(logging.INFO)
        
        # 避免重复添加处理器
        if self.logger.handlers:
            return
            
        # 创建日志目录
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 文件处理器（轮转日志）
        log_file = self.log_dir / "app.log"
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.INFO)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # 中文格式化器
        file_formatter = ChineseFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        console_formatter = ChineseFormatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        
        file_handler.setFormatter(file_formatter)
        console_handler.setFormatter(console_formatter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
    def info(self, message: str):
        """记录信息日志"""
        self.logger.info(message)
        
    def warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(message)
        
    def error(self, message: str):
        """记录错误日志"""
        self.logger.error(message)
        
    def debug(self, message: str):
        """记录调试日志"""
        self.logger.debug(message)
        
    def critical(self, message: str):
        """记录严重错误日志"""
        self.logger.critical(message)

# 全局日志实例
_global_logger: Optional[Logger] = None

def setup_logger(name: str = "AI小说助手", log_dir: str = "logs") -> Logger:
    """设置全局日志器"""
    global _global_logger
    _global_logger = Logger(name, log_dir)
    return _global_logger

def get_logger() -> Logger:
    """获取全局日志器"""
    global _global_logger
    if _global_logger is None:
        _global_logger = setup_logger()
    return _global_logger

class ErrorHandler:
    """错误处理器"""
    
    def __init__(self, logger: Optional[Logger] = None):
        self.logger = logger or get_logger()
        
    def handle_api_error(self, error: Exception, context: str = "") -> str:
        """处理API错误"""
        error_msg = f"API调用失败: {str(error)}"
        if context:
            error_msg = f"{context} - {error_msg}"
            
        self.logger.error(error_msg)
        
        # 返回用户友好的错误信息
        error_str = str(error).lower()
        if "timeout" in error_str:
            return "网络连接超时，请检查网络设置"
        elif "unauthorized" in error_str or "401" in error_str:
            return "API密钥无效，请检查配置"
        elif "quota" in error_str or "429" in error_str:
            return "API配额已用完，请检查账户余额"
        elif "404" in error_str:
            return "API地址无效，请检查配置"
        else:
            return "AI服务暂时不可用，请稍后重试"
            
    def handle_file_error(self, error: Exception, file_path: str = "") -> str:
        """处理文件错误"""
        error_msg = f"文件操作失败: {str(error)}"
        if file_path:
            error_msg = f"文件 {file_path} 操作失败: {str(error)}"
            
        self.logger.error(error_msg)
        
        error_str = str(error).lower()
        if "permission" in error_str:
            return "文件权限不足，请检查文件权限"
        elif "not found" in error_str:
            return "文件不存在，请检查文件路径"
        elif "disk" in error_str or "space" in error_str:
            return "磁盘空间不足，请清理磁盘空间"
        else:
            return "文件操作失败，请检查文件状态"
            
    def handle_database_error(self, error: Exception) -> str:
        """处理数据库错误"""
        error_msg = f"数据库操作失败: {str(error)}"
        self.logger.error(error_msg)
        return "数据保存失败，请检查数据库状态"
        
    def handle_validation_error(self, error: Exception) -> str:
        """处理验证错误"""
        error_msg = f"数据验证失败: {str(error)}"
        self.logger.warning(error_msg)
        return str(error)  # 验证错误通常包含用户友好的信息
        
    def handle_unknown_error(self, error: Exception, context: str = "") -> str:
        """处理未知错误"""
        error_msg = f"未知错误: {str(error)}"
        if context:
            error_msg = f"{context} - {error_msg}"
            
        self.logger.error(error_msg)
        return "操作失败，请稍后重试"
