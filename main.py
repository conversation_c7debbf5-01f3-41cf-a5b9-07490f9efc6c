#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI小说助手 - 主程序入口
基于PySide6的桌面应用程序，专为网络小说创作者设计
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt, QTranslator, QLocale
from PySide6.QtGui import QIcon, QFont

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ui.main_window import MainWindow
from config.settings import AppSettings
from utils.logger import setup_logger

class AINovelAssistant:
    """AI小说助手应用程序主类"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.settings = None
        self.logger = None
        
    def setup_application(self):
        """设置应用程序基本配置"""
        # 创建QApplication实例
        self.app = QApplication(sys.argv)
        
        # 设置应用程序基本信息
        self.app.setApplicationName("AI小说助手")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("AI小说助手开发团队")
        self.app.setOrganizationDomain("ai-novel-assistant.com")
        
        # 设置应用程序图标
        icon_path = project_root / "data" / "icons" / "app_icon.png"
        if icon_path.exists():
            self.app.setWindowIcon(QIcon(str(icon_path)))
        
        # 设置高DPI支持
        self.app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        self.app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 设置默认字体
        font = QFont("Microsoft YaHei", 9)
        self.app.setFont(font)
        
    def setup_logging(self):
        """设置日志系统"""
        self.logger = setup_logger()
        self.logger.info("AI小说助手启动中...")
        
    def setup_settings(self):
        """设置配置管理"""
        self.settings = AppSettings()
        self.logger.info("配置系统初始化完成")
        
    def create_main_window(self):
        """创建主窗口"""
        self.main_window = MainWindow()
        self.logger.info("主窗口创建完成")
        
    def show_main_window(self):
        """显示主窗口"""
        self.main_window.show()
        
        # 恢复窗口状态
        if self.settings.get("window.remember_geometry", True):
            geometry = self.settings.get("window.geometry")
            if geometry:
                self.main_window.restoreGeometry(geometry)
            
            state = self.settings.get("window.state")
            if state:
                self.main_window.restoreState(state)
        
        self.logger.info("主窗口显示完成")
        
    def setup_signal_handlers(self):
        """设置信号处理"""
        # 应用程序退出时保存设置
        self.app.aboutToQuit.connect(self.on_about_to_quit)
        
    def on_about_to_quit(self):
        """应用程序即将退出时的处理"""
        if self.main_window and self.settings:
            # 保存窗口状态
            if self.settings.get("window.remember_geometry", True):
                self.settings.set("window.geometry", self.main_window.saveGeometry())
                self.settings.set("window.state", self.main_window.saveState())
            
            # 保存其他设置
            self.settings.save()
            
        self.logger.info("AI小说助手正常退出")
        
    def run(self):
        """运行应用程序"""
        try:
            # 初始化各个组件
            self.setup_application()
            self.setup_logging()
            self.setup_settings()
            self.create_main_window()
            self.setup_signal_handlers()
            
            # 显示主窗口
            self.show_main_window()
            
            # 启动事件循环
            return self.app.exec()
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"应用程序启动失败: {e}")
            else:
                print(f"应用程序启动失败: {e}")
            return 1

def main():
    """主函数"""
    # 创建应用程序实例
    app = AINovelAssistant()
    
    # 运行应用程序
    exit_code = app.run()
    
    # 退出
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
