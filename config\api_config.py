# -*- coding: utf-8 -*-
"""
API配置管理
管理各种AI模型的API配置信息
"""

import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from cryptography.fernet import Fernet
import base64

@dataclass
class APIEndpoint:
    """API端点配置"""
    name: str
    provider: str  # openai, anthropic, google, etc.
    api_key: str
    base_url: str
    model_name: str
    temperature: float = 0.7
    max_tokens: int = 4000
    timeout: int = 120
    enabled: bool = True
    
class APIConfig:
    """API配置管理类"""
    
    def __init__(self, config_file: str = "api_config.json"):
        self.config_file = Path(config_file)
        self.encryption_key = self._get_or_create_key()
        self.endpoints: Dict[str, APIEndpoint] = {}
        self.load()
        
    def _get_or_create_key(self) -> bytes:
        """获取或创建加密密钥"""
        key_file = Path("api.key")
        if key_file.exists():
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            return key
            
    def _encrypt_data(self, data: str) -> str:
        """加密数据"""
        f = Fernet(self.encryption_key)
        encrypted = f.encrypt(data.encode())
        return base64.b64encode(encrypted).decode()
        
    def _decrypt_data(self, encrypted_data: str) -> str:
        """解密数据"""
        try:
            f = Fernet(self.encryption_key)
            encrypted_bytes = base64.b64decode(encrypted_data.encode())
            decrypted = f.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception:
            return ""
            
    def add_endpoint(self, endpoint: APIEndpoint):
        """添加API端点"""
        self.endpoints[endpoint.name] = endpoint
        self.save()
        
    def remove_endpoint(self, name: str):
        """删除API端点"""
        if name in self.endpoints:
            del self.endpoints[name]
            self.save()
            
    def get_endpoint(self, name: str) -> Optional[APIEndpoint]:
        """获取API端点"""
        return self.endpoints.get(name)
        
    def get_enabled_endpoints(self) -> List[APIEndpoint]:
        """获取启用的API端点"""
        return [ep for ep in self.endpoints.values() if ep.enabled]
        
    def get_endpoints_by_provider(self, provider: str) -> List[APIEndpoint]:
        """根据提供商获取端点"""
        return [ep for ep in self.endpoints.values() if ep.provider == provider]
        
    def test_endpoint(self, name: str) -> bool:
        """测试API端点连接"""
        endpoint = self.get_endpoint(name)
        if not endpoint:
            return False
            
        # TODO: 实现具体的连接测试逻辑
        # 这里应该根据不同的provider调用相应的测试API
        return True
        
    def auto_detect_and_fix_url(self, provider: str, url: str) -> str:
        """智能检测并修正API地址"""
        url_patterns = {
            "openai": {
                "base": "https://api.openai.com/v1",
                "suffixes": ["/chat/completions", "/completions"]
            },
            "anthropic": {
                "base": "https://api.anthropic.com",
                "suffixes": ["/v1/messages"]
            },
            "google": {
                "base": "https://generativelanguage.googleapis.com",
                "suffixes": ["/v1beta/models"]
            }
        }
        
        if provider not in url_patterns:
            return url
            
        pattern = url_patterns[provider]
        
        # 如果URL不包含基础地址，添加它
        if pattern["base"] not in url:
            if not url.startswith("http"):
                url = pattern["base"] + "/" + url.lstrip("/")
            else:
                # 可能是自定义域名，保持原样
                pass
                
        # 检查是否需要添加后缀
        has_suffix = any(suffix in url for suffix in pattern["suffixes"])
        if not has_suffix and provider == "openai":
            if not url.endswith("/"):
                url += "/"
            url += "chat/completions"
            
        return url
        
    def load(self):
        """加载配置"""
        if not self.config_file.exists():
            self._create_default_config()
            return
            
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            for name, config in data.items():
                # 解密API密钥
                if 'api_key' in config and config['api_key']:
                    config['api_key'] = self._decrypt_data(config['api_key'])
                    
                endpoint = APIEndpoint(**config)
                self.endpoints[name] = endpoint
                
        except Exception as e:
            print(f"API配置加载失败: {e}")
            self._create_default_config()
            
    def save(self):
        """保存配置"""
        try:
            data = {}
            for name, endpoint in self.endpoints.items():
                config = asdict(endpoint)
                # 加密API密钥
                if config['api_key']:
                    config['api_key'] = self._encrypt_data(config['api_key'])
                data[name] = config
                
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"API配置保存失败: {e}")
            
    def _create_default_config(self):
        """创建默认配置"""
        # 创建一些示例配置（API密钥为空）
        default_endpoints = [
            APIEndpoint(
                name="OpenAI GPT-4",
                provider="openai",
                api_key="",
                base_url="https://api.openai.com/v1",
                model_name="gpt-4",
                temperature=0.7,
                max_tokens=4000
            ),
            APIEndpoint(
                name="OpenAI GPT-3.5",
                provider="openai", 
                api_key="",
                base_url="https://api.openai.com/v1",
                model_name="gpt-3.5-turbo",
                temperature=0.7,
                max_tokens=4000
            ),
            APIEndpoint(
                name="Claude-3",
                provider="anthropic",
                api_key="",
                base_url="https://api.anthropic.com",
                model_name="claude-3-sonnet-20240229",
                temperature=0.7,
                max_tokens=4000
            )
        ]
        
        for endpoint in default_endpoints:
            self.endpoints[endpoint.name] = endpoint
            
        self.save()
