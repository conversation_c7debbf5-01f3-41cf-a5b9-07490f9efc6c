# -*- coding: utf-8 -*-
"""
主窗口类
AI小说助手的主界面，包含导航菜单和内容区域
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
import sys
from pathlib import Path

from config.ui_config import UIConfig
from config.settings import AppSettings
from utils.ui_utils import UIUtils
from utils.logger import get_logger

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.ui_config = UIConfig()
        self.settings = AppSettings()
        self.logger = get_logger()
        
        # 初始化UI组件
        self.navigation_widget = None
        self.content_stack = None
        self.nav_buttons = {}
        self.current_page = None
        
        # 初始化界面
        self.init_ui()
        self.init_menu()
        self.init_toolbar()
        self.init_statusbar()

        # 应用样式
        self.apply_styles()

        # 设置窗口属性
        self.setup_window()

        # 初始化页面（放在最后，因为会调用switch_page）
        self.init_pages()
        
    def init_ui(self):
        """初始化UI"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建左侧导航
        self.navigation_widget = self.create_navigation()
        main_layout.addWidget(self.navigation_widget)
        
        # 创建右侧内容区域
        self.content_stack = QStackedWidget()
        main_layout.addWidget(self.content_stack)
        
        # 设置布局比例
        main_layout.setStretch(0, 0)  # 导航固定宽度
        main_layout.setStretch(1, 1)  # 内容区域自适应
        
    def create_navigation(self) -> QWidget:
        """创建导航菜单"""
        nav_widget = QWidget()
        nav_widget.setObjectName("navigation")
        nav_widget.setFixedWidth(self.ui_config.get_size("navigation.width"))
        
        layout = QVBoxLayout(nav_widget)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(4)
        
        # 应用标题
        title_label = QLabel("AI小说助手")
        title_label.setFont(self.ui_config.get_font("title"))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {self.ui_config.colors.TEXT_PRIMARY};
                padding: 16px 8px;
                font-weight: bold;
            }}
        """)
        layout.addWidget(title_label)
        
        # 添加分隔线
        separator = UIUtils.create_separator()
        layout.addWidget(separator)
        
        # 导航按钮列表
        nav_items = [
            ("大纲生成", "outline_generation", "📝"),
            ("大纲编辑", "outline_editing", "✏️"),
            ("章节编辑", "chapter_editing", "📄"),
            ("章节生成", "chapter_generation", "🤖"),
            ("章节分析", "chapter_analysis", "📊"),
            ("人物编辑", "character_editing", "👥"),
            ("人物关系图", "character_relationships", "🕸️"),
            ("统计信息", "statistics", "📈"),
            ("AI聊天", "ai_chat", "💬"),
            ("提示词库", "prompt_library", "📚"),
            ("上下文管理", "context_management", "🔗"),
            ("向量库检索", "vector_search", "🔍"),
            ("设置", "settings", "⚙️")
        ]
        
        for text, page_id, icon in nav_items:
            btn = self.create_nav_button(text, page_id, icon)
            layout.addWidget(btn)
            self.nav_buttons[page_id] = btn
            
        layout.addStretch()
        return nav_widget
        
    def create_nav_button(self, text: str, page_id: str, icon: str) -> QPushButton:
        """创建导航按钮"""
        btn = QPushButton(f"{icon} {text}")
        btn.setCheckable(True)
        btn.setObjectName("nav_button")
        btn.clicked.connect(lambda: self.switch_page(page_id))
        
        # 设置按钮样式
        btn.setStyleSheet(f"""
            QPushButton#nav_button {{
                text-align: left;
                padding: 12px 16px;
                border: none;
                background-color: transparent;
                color: {self.ui_config.colors.TEXT_PRIMARY};
                font-size: 14px;
                border-radius: 6px;
                margin: 1px;
                min-height: 36px;
            }}
            QPushButton#nav_button:hover {{
                background-color: {self.ui_config.colors.NAV_HOVER};
            }}
            QPushButton#nav_button:checked {{
                background-color: {self.ui_config.colors.PRIMARY};
                color: white;
                font-weight: bold;
            }}
        """)
        
        return btn

    def init_pages(self):
        """初始化各功能页面"""
        # 导入各页面模块
        from ui.components.outline_generation_page import OutlineGenerationPage
        from ui.components.outline_editing_page import OutlineEditingPage
        from ui.components.chapter_editing_page import ChapterEditingPage
        from ui.components.chapter_generation_page import ChapterGenerationPage
        from ui.components.chapter_analysis_page import ChapterAnalysisPage
        from ui.components.character_editing_page import CharacterEditingPage
        from ui.components.character_relationships_page import CharacterRelationshipsPage
        from ui.components.statistics_page import StatisticsPage
        from ui.components.ai_chat_page import AIChatPage
        from ui.components.prompt_library_page import PromptLibraryPage
        from ui.components.context_management_page import ContextManagementPage
        from ui.components.vector_search_page import VectorSearchPage
        from ui.components.settings_page import SettingsPage

        # 创建页面实例
        pages = {
            "outline_generation": OutlineGenerationPage(),
            "outline_editing": OutlineEditingPage(),
            "chapter_editing": ChapterEditingPage(),
            "chapter_generation": ChapterGenerationPage(),
            "chapter_analysis": ChapterAnalysisPage(),
            "character_editing": CharacterEditingPage(),
            "character_relationships": CharacterRelationshipsPage(),
            "statistics": StatisticsPage(),
            "ai_chat": AIChatPage(),
            "prompt_library": PromptLibraryPage(),
            "context_management": ContextManagementPage(),
            "vector_search": VectorSearchPage(),
            "settings": SettingsPage()
        }

        # 添加页面到堆栈
        for page_id, page in pages.items():
            self.content_stack.addWidget(page)

        # 默认显示大纲生成页面
        self.switch_page("outline_generation")

    def init_menu(self):
        """初始化菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")

        new_action = QAction("新建项目(&N)", self)
        new_action.setShortcut(QKeySequence.New)
        new_action.triggered.connect(self.new_project)
        file_menu.addAction(new_action)

        open_action = QAction("打开项目(&O)", self)
        open_action.setShortcut(QKeySequence.Open)
        open_action.triggered.connect(self.open_project)
        file_menu.addAction(open_action)

        file_menu.addSeparator()

        save_action = QAction("保存项目(&S)", self)
        save_action.setShortcut(QKeySequence.Save)
        save_action.triggered.connect(self.save_project)
        file_menu.addAction(save_action)

        save_as_action = QAction("另存为(&A)", self)
        save_as_action.setShortcut(QKeySequence.SaveAs)
        save_as_action.triggered.connect(self.save_project_as)
        file_menu.addAction(save_as_action)

        file_menu.addSeparator()

        export_action = QAction("导出文本(&E)", self)
        export_action.triggered.connect(self.export_text)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")

        undo_action = QAction("撤销(&U)", self)
        undo_action.setShortcut(QKeySequence.Undo)
        edit_menu.addAction(undo_action)

        redo_action = QAction("重做(&R)", self)
        redo_action.setShortcut(QKeySequence.Redo)
        edit_menu.addAction(redo_action)

        edit_menu.addSeparator()

        cut_action = QAction("剪切(&T)", self)
        cut_action.setShortcut(QKeySequence.Cut)
        edit_menu.addAction(cut_action)

        copy_action = QAction("复制(&C)", self)
        copy_action.setShortcut(QKeySequence.Copy)
        edit_menu.addAction(copy_action)

        paste_action = QAction("粘贴(&P)", self)
        paste_action.setShortcut(QKeySequence.Paste)
        edit_menu.addAction(paste_action)

        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")

        ai_settings_action = QAction("AI配置(&A)", self)
        ai_settings_action.triggered.connect(self.show_ai_settings)
        tools_menu.addAction(ai_settings_action)

        preferences_action = QAction("首选项(&P)", self)
        preferences_action.triggered.connect(self.show_preferences)
        tools_menu.addAction(preferences_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")

        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def init_toolbar(self):
        """初始化工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # 新建项目
        new_action = QAction("新建", self)
        new_action.setToolTip("新建项目")
        new_action.triggered.connect(self.new_project)
        toolbar.addAction(new_action)

        # 打开项目
        open_action = QAction("打开", self)
        open_action.setToolTip("打开项目")
        open_action.triggered.connect(self.open_project)
        toolbar.addAction(open_action)

        # 保存项目
        save_action = QAction("保存", self)
        save_action.setToolTip("保存项目")
        save_action.triggered.connect(self.save_project)
        toolbar.addAction(save_action)

        toolbar.addSeparator()

        # AI生成
        generate_action = QAction("AI生成", self)
        generate_action.setToolTip("AI生成内容")
        generate_action.triggered.connect(self.ai_generate)
        toolbar.addAction(generate_action)

    def init_statusbar(self):
        """初始化状态栏"""
        statusbar = self.statusBar()

        # 状态标签
        self.status_label = QLabel("就绪")
        statusbar.addWidget(self.status_label)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        statusbar.addPermanentWidget(self.progress_bar)

        # 字数统计
        self.word_count_label = QLabel("字数: 0")
        statusbar.addPermanentWidget(self.word_count_label)

    def apply_styles(self):
        """应用样式表"""
        # 主窗口样式
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {self.ui_config.colors.BACKGROUND};
                color: {self.ui_config.colors.TEXT_PRIMARY};
            }}

            QWidget#navigation {{
                background-color: {self.ui_config.colors.NAV_BACKGROUND};
                border-right: 1px solid {self.ui_config.colors.DIVIDER};
            }}

            QMenuBar {{
                background-color: {self.ui_config.colors.SURFACE};
                color: {self.ui_config.colors.TEXT_PRIMARY};
                border-bottom: 1px solid {self.ui_config.colors.DIVIDER};
                padding: 4px;
            }}

            QMenuBar::item {{
                background-color: transparent;
                padding: 4px 8px;
                border-radius: 4px;
            }}

            QMenuBar::item:selected {{
                background-color: {self.ui_config.colors.PRIMARY_LIGHT};
            }}

            QMenu {{
                background-color: {self.ui_config.colors.SURFACE};
                color: {self.ui_config.colors.TEXT_PRIMARY};
                border: 1px solid {self.ui_config.colors.DIVIDER};
                border-radius: 4px;
                padding: 4px;
            }}

            QMenu::item {{
                padding: 6px 12px;
                border-radius: 4px;
            }}

            QMenu::item:selected {{
                background-color: {self.ui_config.colors.PRIMARY_LIGHT};
            }}

            QToolBar {{
                background-color: {self.ui_config.colors.SURFACE};
                border-bottom: 1px solid {self.ui_config.colors.DIVIDER};
                spacing: 4px;
                padding: 4px;
            }}

            QStatusBar {{
                background-color: {self.ui_config.colors.SURFACE};
                border-top: 1px solid {self.ui_config.colors.DIVIDER};
                color: {self.ui_config.colors.TEXT_SECONDARY};
            }}
        """)

    def setup_window(self):
        """设置窗口属性"""
        # 设置窗口标题
        self.setWindowTitle("AI小说助手 v1.0.0")

        # 设置窗口图标
        icon_path = Path("data/icons/app_icon.png")
        if icon_path.exists():
            self.setWindowIcon(QIcon(str(icon_path)))

        # 设置窗口大小
        self.resize(
            self.ui_config.get_size("window.default_width"),
            self.ui_config.get_size("window.default_height")
        )

        # 设置最小尺寸
        self.setMinimumSize(
            self.ui_config.get_size("window.min_width"),
            self.ui_config.get_size("window.min_height")
        )

        # 居中显示
        UIUtils.center_window(self)

    def switch_page(self, page_id: str):
        """切换页面"""
        # 更新导航按钮状态
        for btn_id, btn in self.nav_buttons.items():
            btn.setChecked(btn_id == page_id)

        # 切换页面
        page_index = list(self.nav_buttons.keys()).index(page_id)
        self.content_stack.setCurrentIndex(page_index)
        self.current_page = page_id

        # 更新状态栏
        page_names = {
            "outline_generation": "大纲生成",
            "outline_editing": "大纲编辑",
            "chapter_editing": "章节编辑",
            "chapter_generation": "章节生成",
            "chapter_analysis": "章节分析",
            "character_editing": "人物编辑",
            "character_relationships": "人物关系图",
            "statistics": "统计信息",
            "ai_chat": "AI聊天",
            "prompt_library": "提示词库",
            "context_management": "上下文管理",
            "vector_search": "向量库检索",
            "settings": "设置"
        }

        page_name = page_names.get(page_id, "未知页面")
        self.status_label.setText(f"当前页面: {page_name}")

        self.logger.info(f"切换到页面: {page_name}")

    # 菜单事件处理方法
    def new_project(self):
        """新建项目"""
        self.logger.info("新建项目")
        # TODO: 实现新建项目逻辑

    def open_project(self):
        """打开项目"""
        self.logger.info("打开项目")
        # TODO: 实现打开项目逻辑

    def save_project(self):
        """保存项目"""
        self.logger.info("保存项目")
        # TODO: 实现保存项目逻辑

    def save_project_as(self):
        """另存为项目"""
        self.logger.info("另存为项目")
        # TODO: 实现另存为逻辑

    def export_text(self):
        """导出文本"""
        self.logger.info("导出文本")
        # TODO: 实现导出文本逻辑

    def show_ai_settings(self):
        """显示AI设置"""
        self.logger.info("显示AI设置")
        # TODO: 实现AI设置对话框

    def show_preferences(self):
        """显示首选项"""
        self.logger.info("显示首选项")
        # TODO: 实现首选项对话框

    def show_about(self):
        """显示关于对话框"""
        about_text = """
        <h2>AI小说助手 v1.0.0</h2>
        <p>专为网络小说创作者设计的AI辅助工具</p>
        <p>基于PySide6开发，采用Material Design设计风格</p>
        <p><b>主要功能：</b></p>
        <ul>
        <li>智能大纲生成与编辑</li>
        <li>AI章节生成与分析</li>
        <li>人物设定与关系管理</li>
        <li>多AI模型集成</li>
        <li>向量数据库检索</li>
        <li>统计分析与可视化</li>
        </ul>
        <p>© 2024 AI小说助手开发团队</p>
        """

        QMessageBox.about(self, "关于AI小说助手", about_text)

    def ai_generate(self):
        """AI生成内容"""
        self.logger.info("AI生成内容")
        # TODO: 实现AI生成逻辑

    def update_word_count(self, count: int):
        """更新字数统计"""
        self.word_count_label.setText(f"字数: {count:,}")

    def show_progress(self, message: str = ""):
        """显示进度条"""
        self.progress_bar.setVisible(True)
        if message:
            self.status_label.setText(message)

    def hide_progress(self):
        """隐藏进度条"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("就绪")

    def update_progress(self, value: int):
        """更新进度值"""
        self.progress_bar.setValue(value)
