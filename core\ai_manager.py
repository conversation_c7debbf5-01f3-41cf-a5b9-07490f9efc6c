# -*- coding: utf-8 -*-
"""
AI管理器
负责管理和调用各种AI模型
"""

from typing import Dict, Any, List, Optional
from abc import ABC, abstractmethod
import asyncio
import json

from config.api_config import APIConfig, APIEndpoint
from utils.logger import get_logger, ErrorHandler

class AIProvider(ABC):
    """AI提供商抽象基类"""
    
    def __init__(self, endpoint: APIEndpoint):
        self.endpoint = endpoint
        self.logger = get_logger()
        self.error_handler = ErrorHandler()
        
    @abstractmethod
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """生成文本"""
        pass
        
    @abstractmethod
    async def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """对话"""
        pass

class OpenAIProvider(AIProvider):
    """OpenAI提供商"""
    
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """生成文本"""
        try:
            # TODO: 实现OpenAI API调用
            self.logger.info(f"OpenAI生成文本: {prompt[:50]}...")
            return "这是OpenAI生成的示例文本"
        except Exception as e:
            error_msg = self.error_handler.handle_api_error(e, "OpenAI文本生成")
            raise Exception(error_msg)
            
    async def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """对话"""
        try:
            # TODO: 实现OpenAI Chat API调用
            self.logger.info("OpenAI对话调用")
            return "这是OpenAI的回复"
        except Exception as e:
            error_msg = self.error_handler.handle_api_error(e, "OpenAI对话")
            raise Exception(error_msg)

class AnthropicProvider(AIProvider):
    """Anthropic提供商"""
    
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """生成文本"""
        try:
            # TODO: 实现Anthropic API调用
            self.logger.info(f"Anthropic生成文本: {prompt[:50]}...")
            return "这是Anthropic生成的示例文本"
        except Exception as e:
            error_msg = self.error_handler.handle_api_error(e, "Anthropic文本生成")
            raise Exception(error_msg)
            
    async def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """对话"""
        try:
            # TODO: 实现Anthropic API调用
            self.logger.info("Anthropic对话调用")
            return "这是Anthropic的回复"
        except Exception as e:
            error_msg = self.error_handler.handle_api_error(e, "Anthropic对话")
            raise Exception(error_msg)

class GoogleProvider(AIProvider):
    """Google提供商"""
    
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """生成文本"""
        try:
            # TODO: 实现Google API调用
            self.logger.info(f"Google生成文本: {prompt[:50]}...")
            return "这是Google生成的示例文本"
        except Exception as e:
            error_msg = self.error_handler.handle_api_error(e, "Google文本生成")
            raise Exception(error_msg)
            
    async def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """对话"""
        try:
            # TODO: 实现Google API调用
            self.logger.info("Google对话调用")
            return "这是Google的回复"
        except Exception as e:
            error_msg = self.error_handler.handle_api_error(e, "Google对话")
            raise Exception(error_msg)

class AIManager:
    """AI管理器"""
    
    def __init__(self):
        self.api_config = APIConfig()
        self.providers: Dict[str, AIProvider] = {}
        self.logger = get_logger()
        self.error_handler = ErrorHandler()
        
        # 初始化提供商
        self._init_providers()
        
    def _init_providers(self):
        """初始化AI提供商"""
        provider_classes = {
            "openai": OpenAIProvider,
            "anthropic": AnthropicProvider,
            "google": GoogleProvider
        }
        
        for endpoint_name, endpoint in self.api_config.endpoints.items():
            if endpoint.enabled and endpoint.provider in provider_classes:
                provider_class = provider_classes[endpoint.provider]
                self.providers[endpoint_name] = provider_class(endpoint)
                
        self.logger.info(f"初始化了 {len(self.providers)} 个AI提供商")
        
    def get_available_providers(self) -> List[str]:
        """获取可用的提供商列表"""
        return list(self.providers.keys())
        
    def get_provider(self, name: str) -> Optional[AIProvider]:
        """获取指定的提供商"""
        return self.providers.get(name)
        
    async def generate_outline(self, project_info: Dict[str, Any], provider_name: str = None) -> str:
        """生成大纲"""
        try:
            # 选择提供商
            if not provider_name:
                provider_name = self._get_default_provider()
                
            provider = self.get_provider(provider_name)
            if not provider:
                raise Exception(f"提供商 {provider_name} 不可用")
                
            # 构建提示词
            prompt = self._build_outline_prompt(project_info)
            
            # 生成大纲
            result = await provider.generate_text(prompt)
            
            self.logger.info(f"使用 {provider_name} 生成大纲成功")
            return result
            
        except Exception as e:
            error_msg = self.error_handler.handle_api_error(e, "大纲生成")
            raise Exception(error_msg)
            
    async def generate_chapter(self, chapter_info: Dict[str, Any], provider_name: str = None) -> str:
        """生成章节"""
        try:
            # 选择提供商
            if not provider_name:
                provider_name = self._get_default_provider()
                
            provider = self.get_provider(provider_name)
            if not provider:
                raise Exception(f"提供商 {provider_name} 不可用")
                
            # 构建提示词
            prompt = self._build_chapter_prompt(chapter_info)
            
            # 生成章节
            result = await provider.generate_text(prompt)
            
            self.logger.info(f"使用 {provider_name} 生成章节成功")
            return result
            
        except Exception as e:
            error_msg = self.error_handler.handle_api_error(e, "章节生成")
            raise Exception(error_msg)
            
    async def chat_with_ai(self, messages: List[Dict[str, str]], provider_name: str = None) -> str:
        """与AI对话"""
        try:
            # 选择提供商
            if not provider_name:
                provider_name = self._get_default_provider()
                
            provider = self.get_provider(provider_name)
            if not provider:
                raise Exception(f"提供商 {provider_name} 不可用")
                
            # 进行对话
            result = await provider.chat(messages)
            
            self.logger.info(f"使用 {provider_name} 对话成功")
            return result
            
        except Exception as e:
            error_msg = self.error_handler.handle_api_error(e, "AI对话")
            raise Exception(error_msg)
            
    def _get_default_provider(self) -> str:
        """获取默认提供商"""
        if not self.providers:
            raise Exception("没有可用的AI提供商")
            
        # 优先选择OpenAI
        for name in self.providers.keys():
            if "openai" in name.lower():
                return name
                
        # 否则返回第一个可用的
        return list(self.providers.keys())[0]
        
    def _build_outline_prompt(self, project_info: Dict[str, Any]) -> str:
        """构建大纲生成提示词"""
        prompt = f"""请根据以下信息生成小说大纲：

标题：{project_info.get('title', '未命名')}
类型：{project_info.get('genre', '未知')}
主题：{project_info.get('theme', '未知')}
风格：{project_info.get('style', '未知')}
目标字数：{project_info.get('word_count', '未知')}
章节数量：{project_info.get('chapter_count', '未知')}

故事背景：
{project_info.get('background', '')}

主要角色：
{project_info.get('characters', '')}

核心冲突：
{project_info.get('conflict', '')}

请生成详细的故事大纲，包括：
1. 故事概述
2. 主要情节线
3. 章节安排
4. 人物关系
5. 关键转折点

要求：
- 结构清晰，逻辑合理
- 符合所选类型和风格
- 情节紧凑，冲突明确
- 人物形象鲜明
"""
        return prompt
        
    def _build_chapter_prompt(self, chapter_info: Dict[str, Any]) -> str:
        """构建章节生成提示词"""
        prompt = f"""请根据以下信息生成章节内容：

章节标题：{chapter_info.get('title', '未命名章节')}
章节大纲：{chapter_info.get('outline', '')}
目标字数：{chapter_info.get('target_words', 3000)}

关键情节点：
{chapter_info.get('plot_points', '')}

出场人物：
{chapter_info.get('characters', '')}

前文回顾：
{chapter_info.get('previous_context', '')}

要求：
- 字数控制在目标范围内
- 情节推进自然流畅
- 人物对话生动真实
- 描写细腻生动
- 符合整体风格
"""
        return prompt
        
    def test_provider(self, provider_name: str) -> bool:
        """测试提供商连接"""
        try:
            provider = self.get_provider(provider_name)
            if not provider:
                return False
                
            # TODO: 实现实际的连接测试
            self.logger.info(f"测试提供商 {provider_name} 连接")
            return True
            
        except Exception as e:
            self.logger.error(f"测试提供商 {provider_name} 失败: {e}")
            return False
            
    def reload_config(self):
        """重新加载配置"""
        self.api_config.load()
        self.providers.clear()
        self._init_providers()
        self.logger.info("AI配置重新加载完成")
