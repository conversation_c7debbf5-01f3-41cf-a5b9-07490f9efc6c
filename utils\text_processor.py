# -*- coding: utf-8 -*-
"""
文本处理工具
提供文本分析、处理和优化功能
"""

import re
import jieba
from typing import List, Dict, Any, Tuple
from collections import Counter

class TextProcessor:
    """文本处理类"""
    
    def __init__(self):
        # 初始化jieba分词
        jieba.initialize()
        
    @staticmethod
    def count_words(text: str) -> int:
        """统计字数（中文字符数）"""
        # 移除空白字符
        text = re.sub(r'\s+', '', text)
        # 统计中文字符、英文单词和数字
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        english_words = len(re.findall(r'[a-zA-Z]+', text))
        numbers = len(re.findall(r'\d+', text))
        return chinese_chars + english_words + numbers
        
    @staticmethod
    def count_paragraphs(text: str) -> int:
        """统计段落数"""
        paragraphs = [p.strip() for p in text.split('\n') if p.strip()]
        return len(paragraphs)
        
    @staticmethod
    def count_sentences(text: str) -> int:
        """统计句子数"""
        # 中文句号、问号、感叹号
        sentences = re.split(r'[。！？.!?]+', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        return len(sentences)
        
    @staticmethod
    def extract_keywords(text: str, top_k: int = 10) -> List[Tuple[str, int]]:
        """提取关键词"""
        # 分词
        words = jieba.lcut(text)
        
        # 过滤停用词和标点符号
        stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}
        words = [w for w in words if len(w) > 1 and w not in stop_words and not re.match(r'[^\w]', w)]
        
        # 统计词频
        word_count = Counter(words)
        return word_count.most_common(top_k)
        
    @staticmethod
    def analyze_content_type(text: str) -> Dict[str, float]:
        """分析内容类型分布"""
        total_chars = len(text)
        if total_chars == 0:
            return {"叙述": 0, "对话": 0, "描写": 0, "心理": 0}
            
        # 对话检测（引号内容）
        dialogue_pattern = r'["""]([^"""]*?)["""]|"([^"]*?)"|\'([^\']*?)\'|「([^」]*?)」'
        dialogue_matches = re.findall(dialogue_pattern, text)
        dialogue_chars = sum(len(''.join(match)) for match in dialogue_matches)
        
        # 心理描写检测（心想、想到、觉得等）
        psychology_pattern = r'(心想|想到|觉得|感到|认为|以为|暗想|暗道|心中|内心)[^。！？]*[。！？]'
        psychology_matches = re.findall(psychology_pattern, text)
        psychology_chars = sum(len(match) for match in psychology_matches)
        
        # 环境描写检测（形容词较多的句子）
        description_pattern = r'[^。！？]*[的地得][^。！？]*[。！？]'
        description_matches = re.findall(description_pattern, text)
        description_chars = sum(len(match) for match in description_matches)
        
        # 剩余为叙述
        narrative_chars = total_chars - dialogue_chars - psychology_chars - description_chars
        if narrative_chars < 0:
            narrative_chars = 0
            
        return {
            "叙述": round(narrative_chars / total_chars * 100, 1),
            "对话": round(dialogue_chars / total_chars * 100, 1),
            "描写": round(description_chars / total_chars * 100, 1),
            "心理": round(psychology_chars / total_chars * 100, 1)
        }
        
    @staticmethod
    def detect_ai_patterns(text: str) -> Dict[str, Any]:
        """检测AI生成痕迹"""
        ai_indicators = {
            "重复词汇": 0,
            "机械化表达": 0,
            "逻辑连接词过多": 0,
            "情感表达单一": 0
        }
        
        # 检测重复词汇
        words = jieba.lcut(text)
        word_count = Counter(words)
        repeated_words = [word for word, count in word_count.items() if count > 5 and len(word) > 1]
        ai_indicators["重复词汇"] = len(repeated_words)
        
        # 检测机械化表达
        mechanical_patterns = [
            r'首先.*其次.*最后',
            r'一方面.*另一方面',
            r'总的来说',
            r'综上所述',
            r'不仅.*而且',
            r'虽然.*但是'
        ]
        mechanical_count = sum(len(re.findall(pattern, text)) for pattern in mechanical_patterns)
        ai_indicators["机械化表达"] = mechanical_count
        
        # 检测逻辑连接词
        logic_words = ['因此', '所以', '然而', '但是', '而且', '并且', '同时', '此外', '另外', '总之']
        logic_count = sum(text.count(word) for word in logic_words)
        ai_indicators["逻辑连接词过多"] = logic_count
        
        # 检测情感表达
        emotion_words = ['高兴', '开心', '难过', '伤心', '愤怒', '生气', '惊讶', '害怕', '紧张', '兴奋']
        emotion_count = sum(text.count(word) for word in emotion_words)
        ai_indicators["情感表达单一"] = 1 if emotion_count < 3 else 0
        
        # 计算AI味评分（0-100，越高越像AI生成）
        total_score = sum(ai_indicators.values())
        ai_score = min(total_score * 10, 100)
        
        return {
            "ai_score": ai_score,
            "indicators": ai_indicators,
            "suggestions": TextProcessor._get_ai_reduction_suggestions(ai_indicators)
        }
        
    @staticmethod
    def _get_ai_reduction_suggestions(indicators: Dict[str, int]) -> List[str]:
        """获取降AI味建议"""
        suggestions = []
        
        if indicators["重复词汇"] > 3:
            suggestions.append("减少重复词汇的使用，尝试使用同义词替换")
            
        if indicators["机械化表达"] > 2:
            suggestions.append("避免使用过于规整的逻辑结构，增加自然的表达方式")
            
        if indicators["逻辑连接词过多"] > 5:
            suggestions.append("减少逻辑连接词的使用，让文字更加自然流畅")
            
        if indicators["情感表达单一"] > 0:
            suggestions.append("丰富情感表达，增加更多细腻的情感描写")
            
        if not suggestions:
            suggestions.append("文本表达自然，无明显AI生成痕迹")
            
        return suggestions
        
    @staticmethod
    def extract_character_mentions(text: str, character_names: List[str]) -> Dict[str, int]:
        """提取角色出现次数"""
        mentions = {}
        for name in character_names:
            count = text.count(name)
            if count > 0:
                mentions[name] = count
        return mentions
        
    @staticmethod
    def analyze_dialogue_ratio(text: str) -> float:
        """分析对话比例"""
        total_chars = len(text)
        if total_chars == 0:
            return 0.0
            
        # 提取对话内容
        dialogue_pattern = r'["""]([^"""]*?)["""]|"([^"]*?)"|\'([^\']*?)\'|「([^」]*?)」'
        dialogue_matches = re.findall(dialogue_pattern, text)
        dialogue_chars = sum(len(''.join(match)) for match in dialogue_matches)
        
        return round(dialogue_chars / total_chars * 100, 1)
        
    @staticmethod
    def suggest_improvements(text: str) -> List[str]:
        """提供文本改进建议"""
        suggestions = []
        
        word_count = TextProcessor.count_words(text)
        paragraph_count = TextProcessor.count_paragraphs(text)
        
        # 字数建议
        if word_count < 500:
            suggestions.append("内容较短，建议增加更多细节描写")
        elif word_count > 5000:
            suggestions.append("内容较长，建议适当精简或分段")
            
        # 段落建议
        if paragraph_count < 3:
            suggestions.append("段落较少，建议增加段落分隔，提高可读性")
        elif paragraph_count > word_count / 100:
            suggestions.append("段落过多，建议合并相关内容")
            
        # 对话比例建议
        dialogue_ratio = TextProcessor.analyze_dialogue_ratio(text)
        if dialogue_ratio < 10:
            suggestions.append("对话较少，建议增加人物对话，增强故事的生动性")
        elif dialogue_ratio > 70:
            suggestions.append("对话过多，建议增加叙述和描写内容")
            
        # AI味检测建议
        ai_analysis = TextProcessor.detect_ai_patterns(text)
        if ai_analysis["ai_score"] > 50:
            suggestions.extend(ai_analysis["suggestions"])
            
        return suggestions
        
    @staticmethod
    def format_text(text: str) -> str:
        """格式化文本"""
        # 统一换行符
        text = text.replace('\r\n', '\n').replace('\r', '\n')
        
        # 移除多余空行
        text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)
        
        # 统一标点符号
        text = text.replace('"', '"').replace('"', '"')
        text = text.replace(''', "'").replace(''', "'")
        
        # 移除行首行尾空格
        lines = [line.strip() for line in text.split('\n')]
        text = '\n'.join(lines)
        
        return text.strip()
