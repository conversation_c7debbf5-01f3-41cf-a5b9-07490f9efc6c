# AI小说助手

专为网络小说创作者设计的AI辅助工具，基于PySide6开发，采用Material Design设计风格。

## 功能特性

### 📝 核心功能
- **智能大纲生成** - AI根据设定自动生成详细故事大纲
- **大纲编辑管理** - 可视化编辑和管理章节结构
- **章节内容编辑** - 专业的写作编辑器，支持实时统计
- **AI章节生成** - 根据大纲智能生成章节内容
- **章节内容分析** - 分析文本质量和AI生成痕迹

### 👥 人物管理
- **人物设定编辑** - 详细的角色信息管理
- **人物关系图** - 可视化展示角色关系网络

### 📊 统计分析
- **写作统计** - 字数、进度、效率等数据统计
- **数据可视化** - 图表展示写作进展和分析结果

### 🤖 AI集成
- **多AI模型支持** - 支持OpenAI、Claude、Gemini等
- **AI聊天助手** - 随时咨询写作问题
- **提示词库** - 丰富的写作提示词模板

### 🔍 高级功能
- **上下文管理** - 智能管理故事上下文信息
- **向量库检索** - 基于语义的内容检索
- **项目管理** - 完整的项目保存和管理系统

## 技术架构

### 前端界面
- **PySide6** - 现代化的桌面应用框架
- **Material Design** - 美观统一的设计风格
- **响应式布局** - 适配不同屏幕尺寸

### 后端逻辑
- **Python 3.8+** - 稳定可靠的开发语言
- **异步处理** - 高效的AI API调用
- **模块化设计** - 清晰的代码结构

### 数据存储
- **JSON格式** - 轻量级的项目文件格式
- **本地存储** - 保护用户数据隐私
- **自动备份** - 防止数据丢失

## 安装使用

### 环境要求
- Python 3.8 或更高版本
- Windows 10/11, macOS 10.14+, 或 Linux

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-repo/ai-novel-assistant.git
cd ai-novel-assistant
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **运行程序**
```bash
python run.py
```

### 配置AI服务

首次使用需要配置AI API：

1. 打开应用程序
2. 进入"设置"页面
3. 配置AI API密钥和地址
4. 测试连接确保正常

## 项目结构

```
ai-novel-assistant/
├── main.py                 # 主程序入口
├── run.py                  # 启动脚本
├── requirements.txt        # 依赖包列表
├── config/                 # 配置管理
│   ├── settings.py         # 应用设置
│   ├── api_config.py       # API配置
│   └── ui_config.py        # UI配置
├── core/                   # 核心业务逻辑
│   ├── ai_manager.py       # AI管理器
│   └── project_manager.py  # 项目管理器
├── ui/                     # 用户界面
│   ├── main_window.py      # 主窗口
│   └── components/         # UI组件
├── utils/                  # 工具函数
│   ├── logger.py           # 日志管理
│   ├── file_handler.py     # 文件处理
│   ├── text_processor.py   # 文本处理
│   └── ui_utils.py         # UI工具
└── data/                   # 数据文件
    ├── templates/          # 模板文件
    ├── prompts/            # 提示词库
    └── icons/              # 图标资源
```

## 开发指南

### 代码规范
- 遵循PEP 8编码规范
- 使用类型提示增强代码可读性
- 编写详细的文档字符串

### 测试
```bash
# 运行测试
pytest tests/

# 代码格式检查
flake8 .

# 代码格式化
black .
```

### 构建发布
```bash
# 构建可执行文件
pyinstaller --onefile --windowed main.py
```

## 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用MIT许可证 - 查看[LICENSE](LICENSE)文件了解详情。

## 联系方式

- 项目主页：https://github.com/your-repo/ai-novel-assistant
- 问题反馈：https://github.com/your-repo/ai-novel-assistant/issues
- 邮箱：<EMAIL>

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现核心功能模块
- 支持多AI模型集成
- Material Design界面设计

---

**AI小说助手** - 让创作更智能，让故事更精彩！
