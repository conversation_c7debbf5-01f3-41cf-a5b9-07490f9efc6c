# -*- coding: utf-8 -*-
"""
UI配置管理
管理界面相关的配置和样式
"""

from typing import Dict, Any
from PySide6.QtCore import QSize
from PySide6.QtGui import QColor, QFont

class UIColors:
    """Material Design 颜色系统"""
    
    # 主色调 - 蓝色系（避免紫色系）
    PRIMARY = "#2196F3"          # 蓝色
    PRIMARY_LIGHT = "#64B5F6"    # 浅蓝色
    PRIMARY_DARK = "#1976D2"     # 深蓝色
    
    # 辅助色调
    SECONDARY = "#FF9800"        # 橙色
    SECONDARY_LIGHT = "#FFB74D"  # 浅橙色
    SECONDARY_DARK = "#F57C00"   # 深橙色
    
    # 成功/错误/警告色
    SUCCESS = "#4CAF50"          # 绿色
    ERROR = "#F44336"            # 红色
    WARNING = "#FF9800"          # 橙色
    INFO = "#2196F3"             # 蓝色
    
    # 中性色
    BACKGROUND = "#FAFAFA"       # 背景色
    SURFACE = "#FFFFFF"          # 表面色
    ON_SURFACE = "#212121"       # 表面文字色
    ON_BACKGROUND = "#212121"    # 背景文字色
    
    # 边框和分割线
    DIVIDER = "#E0E0E0"          # 分割线
    OUTLINE = "#BDBDBD"          # 边框
    
    # 文字颜色
    TEXT_PRIMARY = "#212121"     # 主要文字
    TEXT_SECONDARY = "#757575"   # 次要文字
    TEXT_DISABLED = "#BDBDBD"    # 禁用文字
    
    # 导航相关
    NAV_BACKGROUND = "#F5F5F5"   # 导航背景
    NAV_SELECTED = "#E3F2FD"     # 导航选中
    NAV_HOVER = "#BBDEFB"        # 导航悬停

class UIConfig:
    """UI配置管理类"""
    
    def __init__(self):
        self.colors = UIColors()
        self.fonts = self._init_fonts()
        self.sizes = self._init_sizes()
        self.styles = self._init_styles()
        
    def _init_fonts(self) -> Dict[str, QFont]:
        """初始化字体配置"""
        return {
            "default": QFont("Microsoft YaHei", 9),
            "title": QFont("Microsoft YaHei", 14, QFont.Bold),
            "subtitle": QFont("Microsoft YaHei", 12, QFont.Bold),
            "body": QFont("Microsoft YaHei", 9),
            "caption": QFont("Microsoft YaHei", 8),
            "button": QFont("Microsoft YaHei", 9),
            "code": QFont("Consolas", 9)
        }
        
    def _init_sizes(self) -> Dict[str, Any]:
        """初始化尺寸配置"""
        return {
            "window": {
                "min_width": 1200,
                "min_height": 800,
                "default_width": 1400,
                "default_height": 900
            },
            "navigation": {
                "width": 200,
                "button_height": 40,
                "icon_size": QSize(20, 20)
            },
            "toolbar": {
                "height": 40,
                "icon_size": QSize(24, 24)
            },
            "statusbar": {
                "height": 30
            },
            "button": {
                "min_width": 80,
                "height": 32,
                "icon_size": QSize(16, 16)
            },
            "input": {
                "height": 32,
                "padding": 8
            }
        }
        
    def _init_styles(self) -> Dict[str, str]:
        """初始化样式表"""
        return {
            "main_window": f"""
                QMainWindow {{
                    background-color: {self.colors.BACKGROUND};
                    color: {self.colors.TEXT_PRIMARY};
                }}
            """,
            
            "navigation": f"""
                QWidget#navigation {{
                    background-color: {self.colors.NAV_BACKGROUND};
                    border-right: 1px solid {self.colors.DIVIDER};
                }}
                
                QPushButton {{
                    text-align: left;
                    padding: 10px 15px;
                    border: none;
                    background-color: transparent;
                    color: {self.colors.TEXT_PRIMARY};
                    font-size: 14px;
                    border-radius: 4px;
                    margin: 2px;
                }}
                
                QPushButton:hover {{
                    background-color: {self.colors.NAV_HOVER};
                }}
                
                QPushButton:checked {{
                    background-color: {self.colors.PRIMARY};
                    color: white;
                }}
            """,
            
            "material_button": f"""
                QPushButton {{
                    background-color: {self.colors.PRIMARY};
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                    min-width: 80px;
                }}
                
                QPushButton:hover {{
                    background-color: {self.colors.PRIMARY_DARK};
                }}
                
                QPushButton:pressed {{
                    background-color: {self.colors.PRIMARY_DARK};
                }}
                
                QPushButton:disabled {{
                    background-color: {self.colors.TEXT_DISABLED};
                    color: white;
                }}
            """,
            
            "material_input": f"""
                QLineEdit {{
                    border: 2px solid {self.colors.OUTLINE};
                    border-radius: 4px;
                    padding: 8px;
                    background-color: {self.colors.SURFACE};
                    color: {self.colors.TEXT_PRIMARY};
                    font-size: 14px;
                }}
                
                QLineEdit:focus {{
                    border-color: {self.colors.PRIMARY};
                }}
                
                QLineEdit:disabled {{
                    background-color: {self.colors.BACKGROUND};
                    color: {self.colors.TEXT_DISABLED};
                }}
            """,
            
            "material_textarea": f"""
                QTextEdit {{
                    border: 2px solid {self.colors.OUTLINE};
                    border-radius: 4px;
                    padding: 8px;
                    background-color: {self.colors.SURFACE};
                    color: {self.colors.TEXT_PRIMARY};
                    font-size: 14px;
                    line-height: 1.4;
                }}
                
                QTextEdit:focus {{
                    border-color: {self.colors.PRIMARY};
                }}
            """,
            
            "material_combobox": f"""
                QComboBox {{
                    border: 2px solid {self.colors.OUTLINE};
                    border-radius: 4px;
                    padding: 8px;
                    background-color: {self.colors.SURFACE};
                    color: {self.colors.TEXT_PRIMARY};
                    min-width: 120px;
                }}
                
                QComboBox:focus {{
                    border-color: {self.colors.PRIMARY};
                }}
                
                QComboBox::drop-down {{
                    border: none;
                    width: 20px;
                }}
                
                QComboBox::down-arrow {{
                    image: url(data/icons/arrow_down.svg);
                    width: 12px;
                    height: 12px;
                }}
            """,
            
            "progress_bar": f"""
                QProgressBar {{
                    border: 2px solid {self.colors.OUTLINE};
                    border-radius: 4px;
                    text-align: center;
                    background-color: {self.colors.BACKGROUND};
                }}
                
                QProgressBar::chunk {{
                    background-color: {self.colors.PRIMARY};
                    border-radius: 2px;
                }}
            """,
            
            "tab_widget": f"""
                QTabWidget::pane {{
                    border: 1px solid {self.colors.DIVIDER};
                    background-color: {self.colors.SURFACE};
                }}
                
                QTabBar::tab {{
                    background-color: {self.colors.BACKGROUND};
                    color: {self.colors.TEXT_PRIMARY};
                    padding: 8px 16px;
                    margin-right: 2px;
                    border-top-left-radius: 4px;
                    border-top-right-radius: 4px;
                }}
                
                QTabBar::tab:selected {{
                    background-color: {self.colors.PRIMARY};
                    color: white;
                }}
                
                QTabBar::tab:hover {{
                    background-color: {self.colors.PRIMARY_LIGHT};
                }}
            """,
            
            "status_bar": f"""
                QStatusBar {{
                    background-color: {self.colors.SURFACE};
                    border-top: 1px solid {self.colors.DIVIDER};
                    color: {self.colors.TEXT_SECONDARY};
                }}
                
                QStatusBar::item {{
                    border: none;
                }}
            """
        }
        
    def get_style(self, component: str) -> str:
        """获取组件样式"""
        return self.styles.get(component, "")
        
    def get_color(self, color_name: str) -> str:
        """获取颜色值"""
        return getattr(self.colors, color_name.upper(), "#000000")
        
    def get_font(self, font_name: str) -> QFont:
        """获取字体"""
        return self.fonts.get(font_name, self.fonts["default"])
        
    def get_size(self, size_path: str) -> Any:
        """获取尺寸配置"""
        keys = size_path.split('.')
        value = self.sizes
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return None
                
        return value
