# -*- coding: utf-8 -*-
"""
文件处理工具
提供文件读写、项目管理等功能
"""

import json
import os
import shutil
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime
import zipfile

class FileHandler:
    """文件处理类"""
    
    @staticmethod
    def read_text_file(file_path: str, encoding: str = 'utf-8') -> str:
        """读取文本文件"""
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
        except Exception as e:
            raise Exception(f"读取文件失败: {e}")
            
    @staticmethod
    def write_text_file(file_path: str, content: str, encoding: str = 'utf-8'):
        """写入文本文件"""
        try:
            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
        except Exception as e:
            raise Exception(f"写入文件失败: {e}")
            
    @staticmethod
    def read_json_file(file_path: str) -> Dict[str, Any]:
        """读取JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            raise Exception(f"读取JSON文件失败: {e}")
            
    @staticmethod
    def write_json_file(file_path: str, data: Dict[str, Any]):
        """写入JSON文件"""
        try:
            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            raise Exception(f"写入JSON文件失败: {e}")
            
    @staticmethod
    def copy_file(src: str, dst: str):
        """复制文件"""
        try:
            # 确保目标目录存在
            Path(dst).parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(src, dst)
        except Exception as e:
            raise Exception(f"复制文件失败: {e}")
            
    @staticmethod
    def move_file(src: str, dst: str):
        """移动文件"""
        try:
            # 确保目标目录存在
            Path(dst).parent.mkdir(parents=True, exist_ok=True)
            shutil.move(src, dst)
        except Exception as e:
            raise Exception(f"移动文件失败: {e}")
            
    @staticmethod
    def delete_file(file_path: str):
        """删除文件"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception as e:
            raise Exception(f"删除文件失败: {e}")
            
    @staticmethod
    def create_directory(dir_path: str):
        """创建目录"""
        try:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            raise Exception(f"创建目录失败: {e}")
            
    @staticmethod
    def list_files(dir_path: str, pattern: str = "*") -> List[str]:
        """列出目录中的文件"""
        try:
            path = Path(dir_path)
            if not path.exists():
                return []
            return [str(f) for f in path.glob(pattern) if f.is_file()]
        except Exception as e:
            raise Exception(f"列出文件失败: {e}")
            
    @staticmethod
    def get_file_info(file_path: str) -> Dict[str, Any]:
        """获取文件信息"""
        try:
            path = Path(file_path)
            if not path.exists():
                return {}
                
            stat = path.stat()
            return {
                "name": path.name,
                "size": stat.st_size,
                "created": datetime.fromtimestamp(stat.st_ctime),
                "modified": datetime.fromtimestamp(stat.st_mtime),
                "is_file": path.is_file(),
                "is_dir": path.is_dir(),
                "extension": path.suffix
            }
        except Exception as e:
            raise Exception(f"获取文件信息失败: {e}")

class ProjectFileManager:
    """项目文件管理器"""
    
    def __init__(self, project_dir: str = "projects"):
        self.project_dir = Path(project_dir)
        self.project_dir.mkdir(parents=True, exist_ok=True)
        
    def save_project(self, project_data: Dict[str, Any], file_path: str) -> bool:
        """保存项目文件"""
        try:
            # 添加元数据
            project_data["metadata"] = {
                "version": "1.0.0",
                "created": datetime.now().isoformat(),
                "modified": datetime.now().isoformat(),
                "app_version": "1.0.0"
            }
            
            # 保存为.ainovel格式
            if not file_path.endswith('.ainovel'):
                file_path += '.ainovel'
                
            FileHandler.write_json_file(file_path, project_data)
            return True
            
        except Exception as e:
            raise Exception(f"保存项目失败: {e}")
            
    def load_project(self, file_path: str) -> Dict[str, Any]:
        """加载项目文件"""
        try:
            if not file_path.endswith('.ainovel'):
                file_path += '.ainovel'
                
            project_data = FileHandler.read_json_file(file_path)
            
            # 更新最后修改时间
            if "metadata" in project_data:
                project_data["metadata"]["modified"] = datetime.now().isoformat()
                
            return project_data
            
        except Exception as e:
            raise Exception(f"加载项目失败: {e}")
            
    def export_project_text(self, project_data: Dict[str, Any], output_path: str) -> bool:
        """导出项目为文本格式"""
        try:
            content = []
            
            # 添加项目信息
            if "basic_info" in project_data:
                info = project_data["basic_info"]
                content.append(f"标题：{info.get('title', '未命名')}")
                content.append(f"类型：{info.get('genre', '未知')}")
                content.append(f"主题：{info.get('theme', '未知')}")
                content.append(f"风格：{info.get('style', '未知')}")
                content.append("\n" + "="*50 + "\n")
                
            # 添加故事梗概
            if "outline" in project_data:
                outline = project_data["outline"]
                if "summary" in outline:
                    content.append("故事梗概：")
                    content.append(outline["summary"])
                    content.append("\n" + "="*50 + "\n")
                    
            # 添加人物设定
            if "characters" in project_data:
                content.append("人物设定：")
                for char in project_data["characters"]:
                    content.append(f"\n【{char.get('name', '未命名')}】")
                    content.append(f"身份：{char.get('identity', '未知')}")
                    content.append(f"性格：{char.get('personality', '未知')}")
                    if char.get('background'):
                        content.append(f"背景：{char['background']}")
                content.append("\n" + "="*50 + "\n")
                
            # 添加章节内容
            if "chapters" in project_data:
                content.append("正文内容：")
                for chapter in project_data["chapters"]:
                    content.append(f"\n{chapter.get('title', '未命名章节')}")
                    content.append("-" * 30)
                    content.append(chapter.get('content', ''))
                    content.append("")
                    
            # 写入文件
            full_content = "\n".join(content)
            FileHandler.write_text_file(output_path, full_content)
            return True
            
        except Exception as e:
            raise Exception(f"导出文本失败: {e}")
            
    def create_backup(self, project_data: Dict[str, Any], backup_dir: str = "backups") -> str:
        """创建项目备份"""
        try:
            backup_path = Path(backup_dir)
            backup_path.mkdir(parents=True, exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            project_name = project_data.get("basic_info", {}).get("title", "未命名项目")
            backup_file = backup_path / f"{project_name}_backup_{timestamp}.ainovel"
            
            # 保存备份
            FileHandler.write_json_file(str(backup_file), project_data)
            return str(backup_file)
            
        except Exception as e:
            raise Exception(f"创建备份失败: {e}")
            
    def get_recent_projects(self, max_count: int = 10) -> List[Dict[str, Any]]:
        """获取最近的项目列表"""
        try:
            project_files = self.list_files(str(self.project_dir), "*.ainovel")
            recent_projects = []
            
            for file_path in project_files:
                try:
                    file_info = FileHandler.get_file_info(file_path)
                    project_data = FileHandler.read_json_file(file_path)
                    
                    recent_projects.append({
                        "file_path": file_path,
                        "title": project_data.get("basic_info", {}).get("title", "未命名项目"),
                        "modified": file_info["modified"],
                        "size": file_info["size"]
                    })
                except:
                    continue
                    
            # 按修改时间排序
            recent_projects.sort(key=lambda x: x["modified"], reverse=True)
            return recent_projects[:max_count]
            
        except Exception as e:
            raise Exception(f"获取最近项目失败: {e}")
            
    def cleanup_old_backups(self, backup_dir: str = "backups", max_backups: int = 5):
        """清理旧备份文件"""
        try:
            backup_path = Path(backup_dir)
            if not backup_path.exists():
                return
                
            backup_files = list(backup_path.glob("*_backup_*.ainovel"))
            
            # 按修改时间排序
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # 删除多余的备份
            for backup_file in backup_files[max_backups:]:
                backup_file.unlink()
                
        except Exception as e:
            raise Exception(f"清理备份失败: {e}")
