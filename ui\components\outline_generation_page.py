# -*- coding: utf-8 -*-
"""
大纲生成页面
提供AI智能大纲生成功能
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from typing import Dict, Any

from config.ui_config import UIConfig
from utils.ui_utils import UIUtils
from utils.logger import get_logger

class OutlineGenerationPage(QWidget):
    """大纲生成页面"""
    
    def __init__(self):
        super().__init__()
        self.ui_config = UIConfig()
        self.logger = get_logger()
        
        # 初始化UI
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(16)
        
        # 页面标题
        title_label = QLabel("📝 大纲生成")
        title_label.setFont(self.ui_config.get_font("title"))
        title_label.setStyleSheet(f"color: {self.ui_config.colors.TEXT_PRIMARY}; font-weight: bold;")
        layout.addWidget(title_label)
        
        # 创建主要内容区域
        content_widget = self.create_content_area()
        layout.addWidget(content_widget)
        
        layout.addStretch()
        
    def create_content_area(self) -> QWidget:
        """创建内容区域"""
        # 使用水平分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：输入区域
        input_area = self.create_input_area()
        splitter.addWidget(input_area)
        
        # 右侧：输出区域
        output_area = self.create_output_area()
        splitter.addWidget(output_area)
        
        # 设置分割比例
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 1)
        
        return splitter
        
    def create_input_area(self) -> QWidget:
        """创建输入区域"""
        # 创建卡片容器
        input_card = UIUtils.create_card_widget("基本信息设置")
        layout = input_card.layout()
        
        # 创建表单布局
        form_layout = QFormLayout()
        form_layout.setSpacing(12)
        
        # 小说标题
        self.title_input = UIUtils.create_material_input("请输入小说标题")
        form_layout.addRow("小说标题:", self.title_input)
        
        # 小说类型
        self.genre_combo = UIUtils.create_material_combobox([
            "都市言情", "古代言情", "现代言情", "玄幻奇幻", 
            "武侠仙侠", "科幻未来", "悬疑推理", "历史军事",
            "游戏竞技", "同人衍生", "其他类型"
        ])
        form_layout.addRow("小说类型:", self.genre_combo)
        
        # 主题风格
        self.theme_input = UIUtils.create_material_input("如：复仇、成长、爱情、权谋等")
        form_layout.addRow("主题风格:", self.theme_input)
        
        # 写作风格
        self.style_combo = UIUtils.create_material_combobox([
            "轻松幽默", "深沉严肃", "热血激昂", "温馨治愈",
            "悬疑紧张", "浪漫唯美", "现实主义", "魔幻现实"
        ])
        form_layout.addRow("写作风格:", self.style_combo)
        
        # 目标字数
        self.word_count_input = UIUtils.create_material_input("如：100万字", "number")
        form_layout.addRow("目标字数:", self.word_count_input)
        
        # 章节数量
        self.chapter_count_input = UIUtils.create_material_input("如：100章", "number")
        form_layout.addRow("章节数量:", self.chapter_count_input)
        
        layout.addLayout(form_layout)
        
        # 添加分隔线
        separator = UIUtils.create_separator()
        layout.addWidget(separator)
        
        # 故事背景描述
        background_label = QLabel("故事背景描述:")
        background_label.setFont(self.ui_config.get_font("subtitle"))
        layout.addWidget(background_label)
        
        self.background_text = UIUtils.create_material_textarea(
            "请描述故事的背景设定、世界观、时代背景等..."
        )
        self.background_text.setMaximumHeight(120)
        layout.addWidget(self.background_text)
        
        # 主要角色设定
        characters_label = QLabel("主要角色设定:")
        characters_label.setFont(self.ui_config.get_font("subtitle"))
        layout.addWidget(characters_label)
        
        self.characters_text = UIUtils.create_material_textarea(
            "请简要描述主要角色的身份、性格、关系等..."
        )
        self.characters_text.setMaximumHeight(120)
        layout.addWidget(self.characters_text)
        
        # 核心冲突
        conflict_label = QLabel("核心冲突:")
        conflict_label.setFont(self.ui_config.get_font("subtitle"))
        layout.addWidget(conflict_label)
        
        self.conflict_text = UIUtils.create_material_textarea(
            "请描述故事的主要矛盾冲突、核心问题等..."
        )
        self.conflict_text.setMaximumHeight(100)
        layout.addWidget(self.conflict_text)
        
        # 生成按钮
        button_layout = QHBoxLayout()
        
        self.generate_btn = UIUtils.create_material_button("🤖 生成大纲", button_type="primary")
        self.generate_btn.clicked.connect(self.generate_outline)
        button_layout.addWidget(self.generate_btn)
        
        self.clear_btn = UIUtils.create_material_button("🗑️ 清空", button_type="outline")
        self.clear_btn.clicked.connect(self.clear_inputs)
        button_layout.addWidget(self.clear_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        return input_card
        
    def create_output_area(self) -> QWidget:
        """创建输出区域"""
        # 创建卡片容器
        output_card = UIUtils.create_card_widget("生成的大纲")
        layout = output_card.layout()
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 大纲概览标签页
        overview_tab = QWidget()
        overview_layout = QVBoxLayout(overview_tab)
        
        self.outline_overview = UIUtils.create_material_textarea("大纲概览将在这里显示...")
        self.outline_overview.setReadOnly(True)
        overview_layout.addWidget(self.outline_overview)
        
        tab_widget.addTab(overview_tab, "📋 大纲概览")
        
        # 章节详情标签页
        chapters_tab = QWidget()
        chapters_layout = QVBoxLayout(chapters_tab)
        
        self.chapters_tree = QTreeWidget()
        self.chapters_tree.setHeaderLabels(["章节", "标题", "字数", "简介"])
        self.chapters_tree.setAlternatingRowColors(True)
        chapters_layout.addWidget(self.chapters_tree)
        
        tab_widget.addTab(chapters_tab, "📚 章节详情")
        
        # 人物关系标签页
        relations_tab = QWidget()
        relations_layout = QVBoxLayout(relations_tab)
        
        self.relations_text = UIUtils.create_material_textarea("人物关系将在这里显示...")
        self.relations_text.setReadOnly(True)
        relations_layout.addWidget(self.relations_text)
        
        tab_widget.addTab(relations_tab, "👥 人物关系")
        
        layout.addWidget(tab_widget)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.save_btn = UIUtils.create_material_button("💾 保存大纲", button_type="secondary")
        self.save_btn.clicked.connect(self.save_outline)
        self.save_btn.setEnabled(False)
        button_layout.addWidget(self.save_btn)
        
        self.export_btn = UIUtils.create_material_button("📤 导出", button_type="outline")
        self.export_btn.clicked.connect(self.export_outline)
        self.export_btn.setEnabled(False)
        button_layout.addWidget(self.export_btn)
        
        self.edit_btn = UIUtils.create_material_button("✏️ 编辑大纲", button_type="text")
        self.edit_btn.clicked.connect(self.edit_outline)
        self.edit_btn.setEnabled(False)
        button_layout.addWidget(self.edit_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        return output_card
        
    def generate_outline(self):
        """生成大纲"""
        # 获取输入数据
        data = self.get_input_data()
        
        # 验证输入
        if not self.validate_input(data):
            return
            
        # 显示进度
        self.generate_btn.setEnabled(False)
        self.generate_btn.setText("🔄 生成中...")
        
        # TODO: 调用AI生成大纲
        self.logger.info("开始生成大纲")
        
        # 模拟生成过程
        QTimer.singleShot(2000, self.on_outline_generated)
        
    def get_input_data(self) -> Dict[str, Any]:
        """获取输入数据"""
        return {
            "title": self.title_input.text().strip(),
            "genre": self.genre_combo.currentText(),
            "theme": self.theme_input.text().strip(),
            "style": self.style_combo.currentText(),
            "word_count": self.word_count_input.text().strip(),
            "chapter_count": self.chapter_count_input.text().strip(),
            "background": self.background_text.toPlainText().strip(),
            "characters": self.characters_text.toPlainText().strip(),
            "conflict": self.conflict_text.toPlainText().strip()
        }
        
    def validate_input(self, data: Dict[str, Any]) -> bool:
        """验证输入数据"""
        if not data["title"]:
            UIUtils.show_message(self, "输入错误", "请输入小说标题", "warning")
            return False
            
        if not data["background"]:
            UIUtils.show_message(self, "输入错误", "请描述故事背景", "warning")
            return False
            
        return True
        
    def on_outline_generated(self):
        """大纲生成完成"""
        # 恢复按钮状态
        self.generate_btn.setEnabled(True)
        self.generate_btn.setText("🤖 生成大纲")
        
        # 启用操作按钮
        self.save_btn.setEnabled(True)
        self.export_btn.setEnabled(True)
        self.edit_btn.setEnabled(True)
        
        # 显示生成结果（示例）
        self.display_sample_outline()
        
        self.logger.info("大纲生成完成")
        
    def display_sample_outline(self):
        """显示示例大纲"""
        # 这里应该显示实际生成的大纲
        sample_outline = """
        【故事大纲】
        
        第一卷：初入江湖
        - 第1-10章：主角出场，展现天赋，遇到第一个挑战
        - 第11-20章：结识重要伙伴，学习核心技能
        
        第二卷：崭露头角  
        - 第21-40章：参加重要比赛，展现实力
        - 第41-60章：遭遇强敌，经历挫折与成长
        
        第三卷：巅峰对决
        - 第61-80章：最终决战，解决核心冲突
        - 第81-100章：收获成长，故事圆满结束
        """
        
        self.outline_overview.setPlainText(sample_outline)
        
        # 填充章节树
        self.populate_chapters_tree()
        
    def populate_chapters_tree(self):
        """填充章节树"""
        self.chapters_tree.clear()
        
        # 示例章节数据
        volumes = [
            ("第一卷：初入江湖", [
                ("第1章", "天才少年", "3000字", "主角登场，展现天赋"),
                ("第2章", "初次挑战", "3200字", "遇到第一个对手"),
                ("第3章", "意外收获", "2800字", "获得重要道具")
            ]),
            ("第二卷：崭露头角", [
                ("第4章", "新的伙伴", "3100字", "结识重要角色"),
                ("第5章", "技能提升", "3300字", "学习新技能"),
                ("第6章", "团队合作", "2900字", "与伙伴并肩作战")
            ])
        ]
        
        for volume_name, chapters in volumes:
            volume_item = QTreeWidgetItem([volume_name, "", "", ""])
            volume_item.setFont(0, self.ui_config.get_font("subtitle"))
            
            for chapter_num, title, word_count, summary in chapters:
                chapter_item = QTreeWidgetItem([chapter_num, title, word_count, summary])
                volume_item.addChild(chapter_item)
                
            self.chapters_tree.addTopLevelItem(volume_item)
            
        self.chapters_tree.expandAll()
        
    def clear_inputs(self):
        """清空输入"""
        self.title_input.clear()
        self.theme_input.clear()
        self.word_count_input.clear()
        self.chapter_count_input.clear()
        self.background_text.clear()
        self.characters_text.clear()
        self.conflict_text.clear()
        
    def save_outline(self):
        """保存大纲"""
        self.logger.info("保存大纲")
        # TODO: 实现保存逻辑
        
    def export_outline(self):
        """导出大纲"""
        self.logger.info("导出大纲")
        # TODO: 实现导出逻辑
        
    def edit_outline(self):
        """编辑大纲"""
        self.logger.info("编辑大纲")
        # TODO: 切换到大纲编辑页面
