# -*- coding: utf-8 -*-
"""
UI工具函数
提供界面相关的工具函数和组件
"""

from typing import Optional, Dict, Any, List
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from config.ui_config import UIConfig

class UIUtils:
    """UI工具类"""
    
    @staticmethod
    def create_material_button(text: str, 
                             icon: Optional[QIcon] = None,
                             button_type: str = "primary") -> QPushButton:
        """创建Material Design风格按钮"""
        button = QPushButton(text)
        
        if icon:
            button.setIcon(icon)
            button.setIconSize(QSize(16, 16))
            
        # 根据按钮类型设置样式
        ui_config = UIConfig()
        
        if button_type == "primary":
            style = f"""
                QPushButton {{
                    background-color: {ui_config.colors.PRIMARY};
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                    min-width: 80px;
                }}
                QPushButton:hover {{
                    background-color: {ui_config.colors.PRIMARY_DARK};
                }}
                QPushButton:pressed {{
                    background-color: {ui_config.colors.PRIMARY_DARK};
                }}
            """
        elif button_type == "secondary":
            style = f"""
                QPushButton {{
                    background-color: {ui_config.colors.SECONDARY};
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                    min-width: 80px;
                }}
                QPushButton:hover {{
                    background-color: {ui_config.colors.SECONDARY_DARK};
                }}
            """
        elif button_type == "outline":
            style = f"""
                QPushButton {{
                    background-color: transparent;
                    color: {ui_config.colors.PRIMARY};
                    border: 2px solid {ui_config.colors.PRIMARY};
                    padding: 6px 14px;
                    border-radius: 4px;
                    font-weight: bold;
                    min-width: 80px;
                }}
                QPushButton:hover {{
                    background-color: {ui_config.colors.PRIMARY};
                    color: white;
                }}
            """
        else:  # text button
            style = f"""
                QPushButton {{
                    background-color: transparent;
                    color: {ui_config.colors.PRIMARY};
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {ui_config.colors.PRIMARY_LIGHT};
                    color: white;
                }}
            """
            
        button.setStyleSheet(style)
        return button
        
    @staticmethod
    def create_material_input(placeholder: str = "", 
                            input_type: str = "text") -> QLineEdit:
        """创建Material Design风格输入框"""
        input_field = QLineEdit()
        input_field.setPlaceholderText(placeholder)
        
        ui_config = UIConfig()
        style = f"""
            QLineEdit {{
                border: 2px solid {ui_config.colors.OUTLINE};
                border-radius: 4px;
                padding: 8px;
                background-color: {ui_config.colors.SURFACE};
                color: {ui_config.colors.TEXT_PRIMARY};
                font-size: 14px;
            }}
            QLineEdit:focus {{
                border-color: {ui_config.colors.PRIMARY};
            }}
            QLineEdit:disabled {{
                background-color: {ui_config.colors.BACKGROUND};
                color: {ui_config.colors.TEXT_DISABLED};
            }}
        """
        
        input_field.setStyleSheet(style)
        
        # 设置输入类型
        if input_type == "password":
            input_field.setEchoMode(QLineEdit.Password)
        elif input_type == "number":
            input_field.setValidator(QIntValidator())
            
        return input_field
        
    @staticmethod
    def create_material_textarea(placeholder: str = "") -> QTextEdit:
        """创建Material Design风格文本区域"""
        textarea = QTextEdit()
        textarea.setPlaceholderText(placeholder)
        
        ui_config = UIConfig()
        style = f"""
            QTextEdit {{
                border: 2px solid {ui_config.colors.OUTLINE};
                border-radius: 4px;
                padding: 8px;
                background-color: {ui_config.colors.SURFACE};
                color: {ui_config.colors.TEXT_PRIMARY};
                font-size: 14px;
                line-height: 1.4;
            }}
            QTextEdit:focus {{
                border-color: {ui_config.colors.PRIMARY};
            }}
        """
        
        textarea.setStyleSheet(style)
        return textarea
        
    @staticmethod
    def create_material_combobox(items: List[str]) -> QComboBox:
        """创建Material Design风格下拉框"""
        combobox = QComboBox()
        combobox.addItems(items)
        
        ui_config = UIConfig()
        style = f"""
            QComboBox {{
                border: 2px solid {ui_config.colors.OUTLINE};
                border-radius: 4px;
                padding: 8px;
                background-color: {ui_config.colors.SURFACE};
                color: {ui_config.colors.TEXT_PRIMARY};
                min-width: 120px;
            }}
            QComboBox:focus {{
                border-color: {ui_config.colors.PRIMARY};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            QComboBox::down-arrow {{
                width: 12px;
                height: 12px;
            }}
            QComboBox QAbstractItemView {{
                border: 1px solid {ui_config.colors.OUTLINE};
                background-color: {ui_config.colors.SURFACE};
                selection-background-color: {ui_config.colors.PRIMARY_LIGHT};
            }}
        """
        
        combobox.setStyleSheet(style)
        return combobox
        
    @staticmethod
    def create_progress_bar(minimum: int = 0, maximum: int = 100) -> QProgressBar:
        """创建Material Design风格进度条"""
        progress = QProgressBar()
        progress.setMinimum(minimum)
        progress.setMaximum(maximum)
        
        ui_config = UIConfig()
        style = f"""
            QProgressBar {{
                border: 2px solid {ui_config.colors.OUTLINE};
                border-radius: 4px;
                text-align: center;
                background-color: {ui_config.colors.BACKGROUND};
                color: {ui_config.colors.TEXT_PRIMARY};
                font-weight: bold;
            }}
            QProgressBar::chunk {{
                background-color: {ui_config.colors.PRIMARY};
                border-radius: 2px;
            }}
        """
        
        progress.setStyleSheet(style)
        return progress
        
    @staticmethod
    def create_card_widget(title: str = "", content_widget: Optional[QWidget] = None) -> QWidget:
        """创建卡片式容器"""
        card = QWidget()
        layout = QVBoxLayout(card)
        
        ui_config = UIConfig()
        
        # 设置卡片样式
        card.setStyleSheet(f"""
            QWidget {{
                background-color: {ui_config.colors.SURFACE};
                border: 1px solid {ui_config.colors.DIVIDER};
                border-radius: 8px;
                margin: 4px;
            }}
        """)
        
        # 添加标题
        if title:
            title_label = QLabel(title)
            title_label.setFont(ui_config.get_font("subtitle"))
            title_label.setStyleSheet(f"color: {ui_config.colors.TEXT_PRIMARY}; font-weight: bold; padding: 8px;")
            layout.addWidget(title_label)
            
        # 添加内容
        if content_widget:
            layout.addWidget(content_widget)
            
        layout.setContentsMargins(12, 8, 12, 12)
        return card
        
    @staticmethod
    def show_message(parent: QWidget, 
                    title: str, 
                    message: str, 
                    msg_type: str = "info") -> int:
        """显示消息对话框"""
        msg_box = QMessageBox(parent)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        
        if msg_type == "info":
            msg_box.setIcon(QMessageBox.Information)
        elif msg_type == "warning":
            msg_box.setIcon(QMessageBox.Warning)
        elif msg_type == "error":
            msg_box.setIcon(QMessageBox.Critical)
        elif msg_type == "question":
            msg_box.setIcon(QMessageBox.Question)
            msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            
        return msg_box.exec()
        
    @staticmethod
    def create_separator(orientation: Qt.Orientation = Qt.Horizontal) -> QFrame:
        """创建分隔线"""
        separator = QFrame()
        
        if orientation == Qt.Horizontal:
            separator.setFrameShape(QFrame.HLine)
        else:
            separator.setFrameShape(QFrame.VLine)
            
        separator.setFrameShadow(QFrame.Sunken)
        
        ui_config = UIConfig()
        separator.setStyleSheet(f"color: {ui_config.colors.DIVIDER};")
        
        return separator
        
    @staticmethod
    def apply_shadow_effect(widget: QWidget, blur_radius: int = 10):
        """为组件添加阴影效果"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(blur_radius)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 2)
        widget.setGraphicsEffect(shadow)
        
    @staticmethod
    def center_window(window: QWidget, parent: Optional[QWidget] = None):
        """居中显示窗口"""
        if parent:
            parent_geometry = parent.geometry()
            x = parent_geometry.x() + (parent_geometry.width() - window.width()) // 2
            y = parent_geometry.y() + (parent_geometry.height() - window.height()) // 2
            window.move(x, y)
        else:
            # 在屏幕中央显示
            screen = QApplication.primaryScreen().geometry()
            x = (screen.width() - window.width()) // 2
            y = (screen.height() - window.height()) // 2
            window.move(x, y)
