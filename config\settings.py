# -*- coding: utf-8 -*-
"""
应用程序设置管理
提供配置的加载、保存和管理功能
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, Optional
from PySide6.QtCore import QSettings

class AppSettings:
    """应用程序设置管理类"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = Path(config_file)
        self.qt_settings = QSettings("AI小说助手", "MainWindow")
        self.config = self._load_default_config()
        self.load()
        
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            "app": {
                "language": "zh_CN",
                "theme": "light",
                "auto_save": True,
                "auto_save_interval": 300,  # 5分钟
                "max_recent_files": 10,
                "remember_window_state": True
            },
            "ai": {
                "default_model": "openai",
                "request_timeout": 120,
                "max_retries": 3,
                "temperature": 0.7,
                "max_tokens": 4000
            },
            "editor": {
                "font_family": "Microsoft YaHei",
                "font_size": 12,
                "line_spacing": 1.2,
                "word_wrap": True,
                "show_line_numbers": True,
                "auto_indent": True
            },
            "ui": {
                "window_width": 1400,
                "window_height": 900,
                "navigation_width": 200,
                "splitter_sizes": [200, 1200],
                "show_status_bar": True,
                "show_toolbar": True
            },
            "vector_db": {
                "enabled": True,
                "chunk_size": 1000,
                "chunk_overlap": 200,
                "similarity_threshold": 0.7
            },
            "project": {
                "default_save_path": "projects",
                "auto_backup": True,
                "backup_interval": 1800,  # 30分钟
                "max_backups": 5
            }
        }
        
    def load(self):
        """加载配置文件"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    self._merge_config(self.config, loaded_config)
            except Exception as e:
                print(f"配置文件加载失败，使用默认配置: {e}")
                
    def save(self):
        """保存配置文件"""
        try:
            # 确保配置目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"配置文件保存失败: {e}")
            
    def _merge_config(self, default: Dict, loaded: Dict):
        """合并配置"""
        for key, value in loaded.items():
            if key in default:
                if isinstance(value, dict) and isinstance(default[key], dict):
                    self._merge_config(default[key], value)
                else:
                    default[key] = value
                    
    def get(self, key_path: str, default=None) -> Any:
        """获取配置值
        
        Args:
            key_path: 配置路径，如 'app.language'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
                
        return value
        
    def set(self, key_path: str, value: Any):
        """设置配置值
        
        Args:
            key_path: 配置路径，如 'app.language'
            value: 配置值
        """
        keys = key_path.split('.')
        config = self.config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
            
        config[keys[-1]] = value
        
    def get_qt_setting(self, key: str, default=None) -> Any:
        """获取Qt设置"""
        return self.qt_settings.value(key, default)
        
    def set_qt_setting(self, key: str, value: Any):
        """设置Qt设置"""
        self.qt_settings.setValue(key, value)
        
    def reset_to_default(self):
        """重置为默认配置"""
        self.config = self._load_default_config()
        self.save()
        
    def export_config(self, file_path: str):
        """导出配置"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"配置导出失败: {e}")
            return False
            
    def import_config(self, file_path: str):
        """导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
                self._merge_config(self.config, imported_config)
                self.save()
            return True
        except Exception as e:
            print(f"配置导入失败: {e}")
            return False
