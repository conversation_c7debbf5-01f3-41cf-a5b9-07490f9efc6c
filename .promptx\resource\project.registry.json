{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-02T08:09:26.160Z", "updatedAt": "2025-08-02T08:09:26.180Z", "resourceCount": 5}, "resources": [{"id": "project-coordination", "source": "project", "protocol": "execution", "name": "Project Coordination 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/project-coordination.execution.md", "metadata": {"createdAt": "2025-08-02T08:09:26.167Z", "updatedAt": "2025-08-02T08:09:26.167Z", "scannedAt": "2025-08-02T08:09:26.167Z", "path": "role/system-director/execution/project-coordination.execution.md"}}, {"id": "quality-assurance", "source": "project", "protocol": "execution", "name": "Quality Assurance 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/quality-assurance.execution.md", "metadata": {"createdAt": "2025-08-02T08:09:26.169Z", "updatedAt": "2025-08-02T08:09:26.169Z", "scannedAt": "2025-08-02T08:09:26.169Z", "path": "role/system-director/execution/quality-assurance.execution.md"}}, {"id": "system-director", "source": "project", "protocol": "role", "name": "System Director 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/system-director/system-director.role.md", "metadata": {"createdAt": "2025-08-02T08:09:26.170Z", "updatedAt": "2025-08-02T08:09:26.170Z", "scannedAt": "2025-08-02T08:09:26.170Z", "path": "role/system-director/system-director.role.md"}}, {"id": "coordination-mindset", "source": "project", "protocol": "thought", "name": "Coordination Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/coordination-mindset.thought.md", "metadata": {"createdAt": "2025-08-02T08:09:26.174Z", "updatedAt": "2025-08-02T08:09:26.174Z", "scannedAt": "2025-08-02T08:09:26.174Z", "path": "role/system-director/thought/coordination-mindset.thought.md"}}, {"id": "strategic-thinking", "source": "project", "protocol": "thought", "name": "Strategic Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/strategic-thinking.thought.md", "metadata": {"createdAt": "2025-08-02T08:09:26.178Z", "updatedAt": "2025-08-02T08:09:26.178Z", "scannedAt": "2025-08-02T08:09:26.178Z", "path": "role/system-director/thought/strategic-thinking.thought.md"}}], "stats": {"totalResources": 5, "byProtocol": {"execution": 2, "role": 1, "thought": 2}, "bySource": {"project": 5}}}