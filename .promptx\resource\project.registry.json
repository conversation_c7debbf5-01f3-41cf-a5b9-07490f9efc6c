{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-02T08:22:49.566Z", "updatedAt": "2025-08-02T08:22:49.615Z", "resourceCount": 16}, "resources": [{"id": "ai-integration-expert", "source": "project", "protocol": "role", "name": "Ai Integration Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-integration-expert/ai-integration-expert.role.md", "metadata": {"createdAt": "2025-08-02T08:22:49.584Z", "updatedAt": "2025-08-02T08:22:49.584Z", "scannedAt": "2025-08-02T08:22:49.584Z", "path": "role/ai-integration-expert/ai-integration-expert.role.md"}}, {"id": "database-architect", "source": "project", "protocol": "role", "name": "Database Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/database-architect/database-architect.role.md", "metadata": {"createdAt": "2025-08-02T08:22:49.586Z", "updatedAt": "2025-08-02T08:22:49.586Z", "scannedAt": "2025-08-02T08:22:49.586Z", "path": "role/database-architect/database-architect.role.md"}}, {"id": "novel-writing-expert", "source": "project", "protocol": "role", "name": "Novel Writing Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/novel-writing-expert/novel-writing-expert.role.md", "metadata": {"createdAt": "2025-08-02T08:22:49.588Z", "updatedAt": "2025-08-02T08:22:49.588Z", "scannedAt": "2025-08-02T08:22:49.588Z", "path": "role/novel-writing-expert/novel-writing-expert.role.md"}}, {"id": "outline-generator", "source": "project", "protocol": "role", "name": "Outline Generator 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/outline-generator/outline-generator.role.md", "metadata": {"createdAt": "2025-08-02T08:22:49.590Z", "updatedAt": "2025-08-02T08:22:49.590Z", "scannedAt": "2025-08-02T08:22:49.590Z", "path": "role/outline-generator/outline-generator.role.md"}}, {"id": "product-manager", "source": "project", "protocol": "role", "name": "Product Manager 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/product-manager/product-manager.role.md", "metadata": {"createdAt": "2025-08-02T08:22:49.592Z", "updatedAt": "2025-08-02T08:22:49.592Z", "scannedAt": "2025-08-02T08:22:49.592Z", "path": "role/product-manager/product-manager.role.md"}}, {"id": "pyside6-ui-dev", "source": "project", "protocol": "role", "name": "Pyside6 Ui Dev 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/pyside6-ui-dev/pyside6-ui-dev.role.md", "metadata": {"createdAt": "2025-08-02T08:22:49.594Z", "updatedAt": "2025-08-02T08:22:49.594Z", "scannedAt": "2025-08-02T08:22:49.594Z", "path": "role/pyside6-ui-dev/pyside6-ui-dev.role.md"}}, {"id": "python-backend-workflow", "source": "project", "protocol": "execution", "name": "Python Backend Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/python-backend-dev/execution/python-backend-workflow.execution.md", "metadata": {"createdAt": "2025-08-02T08:22:49.598Z", "updatedAt": "2025-08-02T08:22:49.598Z", "scannedAt": "2025-08-02T08:22:49.598Z", "path": "role/python-backend-dev/execution/python-backend-workflow.execution.md"}}, {"id": "python-backend-dev", "source": "project", "protocol": "role", "name": "Python Backend Dev 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/python-backend-dev/python-backend-dev.role.md", "metadata": {"createdAt": "2025-08-02T08:22:49.599Z", "updatedAt": "2025-08-02T08:22:49.599Z", "scannedAt": "2025-08-02T08:22:49.599Z", "path": "role/python-backend-dev/python-backend-dev.role.md"}}, {"id": "python-backend-thinking", "source": "project", "protocol": "thought", "name": "Python Backend Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/python-backend-dev/thought/python-backend-thinking.thought.md", "metadata": {"createdAt": "2025-08-02T08:22:49.601Z", "updatedAt": "2025-08-02T08:22:49.601Z", "scannedAt": "2025-08-02T08:22:49.601Z", "path": "role/python-backend-dev/thought/python-backend-thinking.thought.md"}}, {"id": "qa-engineer", "source": "project", "protocol": "role", "name": "Qa Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/qa-engineer/qa-engineer.role.md", "metadata": {"createdAt": "2025-08-02T08:22:49.602Z", "updatedAt": "2025-08-02T08:22:49.602Z", "scannedAt": "2025-08-02T08:22:49.602Z", "path": "role/qa-engineer/qa-engineer.role.md"}}, {"id": "project-coordination", "source": "project", "protocol": "execution", "name": "Project Coordination 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/project-coordination.execution.md", "metadata": {"createdAt": "2025-08-02T08:22:49.604Z", "updatedAt": "2025-08-02T08:22:49.604Z", "scannedAt": "2025-08-02T08:22:49.604Z", "path": "role/system-director/execution/project-coordination.execution.md"}}, {"id": "quality-assurance", "source": "project", "protocol": "execution", "name": "Quality Assurance 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/quality-assurance.execution.md", "metadata": {"createdAt": "2025-08-02T08:22:49.605Z", "updatedAt": "2025-08-02T08:22:49.605Z", "scannedAt": "2025-08-02T08:22:49.605Z", "path": "role/system-director/execution/quality-assurance.execution.md"}}, {"id": "system-director", "source": "project", "protocol": "role", "name": "System Director 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/system-director/system-director.role.md", "metadata": {"createdAt": "2025-08-02T08:22:49.606Z", "updatedAt": "2025-08-02T08:22:49.606Z", "scannedAt": "2025-08-02T08:22:49.606Z", "path": "role/system-director/system-director.role.md"}}, {"id": "coordination-mindset", "source": "project", "protocol": "thought", "name": "Coordination Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/coordination-mindset.thought.md", "metadata": {"createdAt": "2025-08-02T08:22:49.608Z", "updatedAt": "2025-08-02T08:22:49.608Z", "scannedAt": "2025-08-02T08:22:49.608Z", "path": "role/system-director/thought/coordination-mindset.thought.md"}}, {"id": "strategic-thinking", "source": "project", "protocol": "thought", "name": "Strategic Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/strategic-thinking.thought.md", "metadata": {"createdAt": "2025-08-02T08:22:49.609Z", "updatedAt": "2025-08-02T08:22:49.609Z", "scannedAt": "2025-08-02T08:22:49.609Z", "path": "role/system-director/thought/strategic-thinking.thought.md"}}, {"id": "ux-designer", "source": "project", "protocol": "role", "name": "Ux Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ux-designer/ux-designer.role.md", "metadata": {"createdAt": "2025-08-02T08:22:49.611Z", "updatedAt": "2025-08-02T08:22:49.611Z", "scannedAt": "2025-08-02T08:22:49.611Z", "path": "role/ux-designer/ux-designer.role.md"}}], "stats": {"totalResources": 16, "byProtocol": {"role": 10, "execution": 3, "thought": 3}, "bySource": {"project": 16}}}