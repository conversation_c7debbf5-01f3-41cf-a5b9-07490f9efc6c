{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-02T08:53:33.673Z", "updatedAt": "2025-08-02T08:53:33.733Z", "resourceCount": 16}, "resources": [{"id": "ai-integration-expert", "source": "project", "protocol": "role", "name": "Ai Integration Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-integration-expert/ai-integration-expert.role.md", "metadata": {"createdAt": "2025-08-02T08:53:33.677Z", "updatedAt": "2025-08-02T08:53:33.677Z", "scannedAt": "2025-08-02T08:53:33.677Z", "path": "role/ai-integration-expert/ai-integration-expert.role.md"}}, {"id": "database-architect", "source": "project", "protocol": "role", "name": "Database Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/database-architect/database-architect.role.md", "metadata": {"createdAt": "2025-08-02T08:53:33.678Z", "updatedAt": "2025-08-02T08:53:33.678Z", "scannedAt": "2025-08-02T08:53:33.678Z", "path": "role/database-architect/database-architect.role.md"}}, {"id": "novel-writing-expert", "source": "project", "protocol": "role", "name": "Novel Writing Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/novel-writing-expert/novel-writing-expert.role.md", "metadata": {"createdAt": "2025-08-02T08:53:33.680Z", "updatedAt": "2025-08-02T08:53:33.680Z", "scannedAt": "2025-08-02T08:53:33.680Z", "path": "role/novel-writing-expert/novel-writing-expert.role.md"}}, {"id": "outline-generator", "source": "project", "protocol": "role", "name": "Outline Generator 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/outline-generator/outline-generator.role.md", "metadata": {"createdAt": "2025-08-02T08:53:33.682Z", "updatedAt": "2025-08-02T08:53:33.682Z", "scannedAt": "2025-08-02T08:53:33.682Z", "path": "role/outline-generator/outline-generator.role.md"}}, {"id": "product-manager", "source": "project", "protocol": "role", "name": "Product Manager 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/product-manager/product-manager.role.md", "metadata": {"createdAt": "2025-08-02T08:53:33.683Z", "updatedAt": "2025-08-02T08:53:33.683Z", "scannedAt": "2025-08-02T08:53:33.683Z", "path": "role/product-manager/product-manager.role.md"}}, {"id": "pyside6-ui-dev", "source": "project", "protocol": "role", "name": "Pyside6 Ui Dev 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/pyside6-ui-dev/pyside6-ui-dev.role.md", "metadata": {"createdAt": "2025-08-02T08:53:33.685Z", "updatedAt": "2025-08-02T08:53:33.685Z", "scannedAt": "2025-08-02T08:53:33.685Z", "path": "role/pyside6-ui-dev/pyside6-ui-dev.role.md"}}, {"id": "python-backend-workflow", "source": "project", "protocol": "execution", "name": "Python Backend Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/python-backend-dev/execution/python-backend-workflow.execution.md", "metadata": {"createdAt": "2025-08-02T08:53:33.687Z", "updatedAt": "2025-08-02T08:53:33.687Z", "scannedAt": "2025-08-02T08:53:33.687Z", "path": "role/python-backend-dev/execution/python-backend-workflow.execution.md"}}, {"id": "python-backend-dev", "source": "project", "protocol": "role", "name": "Python Backend Dev 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/python-backend-dev/python-backend-dev.role.md", "metadata": {"createdAt": "2025-08-02T08:53:33.688Z", "updatedAt": "2025-08-02T08:53:33.688Z", "scannedAt": "2025-08-02T08:53:33.688Z", "path": "role/python-backend-dev/python-backend-dev.role.md"}}, {"id": "python-backend-thinking", "source": "project", "protocol": "thought", "name": "Python Backend Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/python-backend-dev/thought/python-backend-thinking.thought.md", "metadata": {"createdAt": "2025-08-02T08:53:33.690Z", "updatedAt": "2025-08-02T08:53:33.690Z", "scannedAt": "2025-08-02T08:53:33.690Z", "path": "role/python-backend-dev/thought/python-backend-thinking.thought.md"}}, {"id": "qa-engineer", "source": "project", "protocol": "role", "name": "Qa Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/qa-engineer/qa-engineer.role.md", "metadata": {"createdAt": "2025-08-02T08:53:33.692Z", "updatedAt": "2025-08-02T08:53:33.692Z", "scannedAt": "2025-08-02T08:53:33.692Z", "path": "role/qa-engineer/qa-engineer.role.md"}}, {"id": "project-coordination", "source": "project", "protocol": "execution", "name": "Project Coordination 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/project-coordination.execution.md", "metadata": {"createdAt": "2025-08-02T08:53:33.694Z", "updatedAt": "2025-08-02T08:53:33.694Z", "scannedAt": "2025-08-02T08:53:33.694Z", "path": "role/system-director/execution/project-coordination.execution.md"}}, {"id": "quality-assurance", "source": "project", "protocol": "execution", "name": "Quality Assurance 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/quality-assurance.execution.md", "metadata": {"createdAt": "2025-08-02T08:53:33.695Z", "updatedAt": "2025-08-02T08:53:33.695Z", "scannedAt": "2025-08-02T08:53:33.695Z", "path": "role/system-director/execution/quality-assurance.execution.md"}}, {"id": "system-director", "source": "project", "protocol": "role", "name": "System Director 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/system-director/system-director.role.md", "metadata": {"createdAt": "2025-08-02T08:53:33.696Z", "updatedAt": "2025-08-02T08:53:33.696Z", "scannedAt": "2025-08-02T08:53:33.696Z", "path": "role/system-director/system-director.role.md"}}, {"id": "coordination-mindset", "source": "project", "protocol": "thought", "name": "Coordination Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/coordination-mindset.thought.md", "metadata": {"createdAt": "2025-08-02T08:53:33.698Z", "updatedAt": "2025-08-02T08:53:33.698Z", "scannedAt": "2025-08-02T08:53:33.698Z", "path": "role/system-director/thought/coordination-mindset.thought.md"}}, {"id": "strategic-thinking", "source": "project", "protocol": "thought", "name": "Strategic Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/strategic-thinking.thought.md", "metadata": {"createdAt": "2025-08-02T08:53:33.728Z", "updatedAt": "2025-08-02T08:53:33.728Z", "scannedAt": "2025-08-02T08:53:33.728Z", "path": "role/system-director/thought/strategic-thinking.thought.md"}}, {"id": "ux-designer", "source": "project", "protocol": "role", "name": "Ux Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ux-designer/ux-designer.role.md", "metadata": {"createdAt": "2025-08-02T08:53:33.729Z", "updatedAt": "2025-08-02T08:53:33.729Z", "scannedAt": "2025-08-02T08:53:33.729Z", "path": "role/ux-designer/ux-designer.role.md"}}], "stats": {"totalResources": 16, "byProtocol": {"role": 10, "execution": 3, "thought": 3}, "bySource": {"project": 16}}}