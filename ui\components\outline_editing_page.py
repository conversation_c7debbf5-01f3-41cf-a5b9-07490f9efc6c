# -*- coding: utf-8 -*-
"""
大纲编辑页面
提供大纲的详细编辑和管理功能
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from typing import Dict, Any, List

from config.ui_config import UIConfig
from utils.ui_utils import UIUtils
from utils.logger import get_logger

class OutlineEditingPage(QWidget):
    """大纲编辑页面"""
    
    def __init__(self):
        super().__init__()
        self.ui_config = UIConfig()
        self.logger = get_logger()
        
        # 数据存储
        self.outline_data = {}
        self.current_chapter = None
        
        # 初始化UI
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(16)
        
        # 页面标题和工具栏
        header_layout = QHBoxLayout()
        
        title_label = QLabel("✏️ 大纲编辑")
        title_label.setFont(self.ui_config.get_font("title"))
        title_label.setStyleSheet(f"color: {self.ui_config.colors.TEXT_PRIMARY}; font-weight: bold;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # 工具按钮
        self.add_volume_btn = UIUtils.create_material_button("📚 添加卷", button_type="primary")
        self.add_volume_btn.clicked.connect(self.add_volume)
        header_layout.addWidget(self.add_volume_btn)
        
        self.add_chapter_btn = UIUtils.create_material_button("📄 添加章节", button_type="secondary")
        self.add_chapter_btn.clicked.connect(self.add_chapter)
        header_layout.addWidget(self.add_chapter_btn)
        
        layout.addLayout(header_layout)
        
        # 主要内容区域
        content_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：大纲树形结构
        left_panel = self.create_outline_tree_panel()
        content_splitter.addWidget(left_panel)
        
        # 右侧：章节详情编辑
        right_panel = self.create_chapter_detail_panel()
        content_splitter.addWidget(right_panel)
        
        # 设置分割比例
        content_splitter.setStretchFactor(0, 1)
        content_splitter.setStretchFactor(1, 2)
        
        layout.addWidget(content_splitter)
        
    def create_outline_tree_panel(self) -> QWidget:
        """创建大纲树形面板"""
        panel = UIUtils.create_card_widget("大纲结构")
        layout = panel.layout()
        
        # 搜索框
        search_layout = QHBoxLayout()
        self.search_input = UIUtils.create_material_input("搜索章节...")
        self.search_input.textChanged.connect(self.filter_chapters)
        search_layout.addWidget(self.search_input)
        
        self.search_btn = UIUtils.create_material_button("🔍", button_type="outline")
        search_layout.addWidget(self.search_btn)
        
        layout.addLayout(search_layout)
        
        # 大纲树
        self.outline_tree = QTreeWidget()
        self.outline_tree.setHeaderLabels(["章节", "字数", "状态"])
        self.outline_tree.setAlternatingRowColors(True)
        self.outline_tree.itemClicked.connect(self.on_chapter_selected)
        self.outline_tree.setContextMenuPolicy(Qt.CustomContextMenu)
        self.outline_tree.customContextMenuRequested.connect(self.show_context_menu)
        
        # 设置列宽
        self.outline_tree.setColumnWidth(0, 200)
        self.outline_tree.setColumnWidth(1, 80)
        self.outline_tree.setColumnWidth(2, 80)
        
        layout.addWidget(self.outline_tree)
        
        # 统计信息
        stats_layout = QHBoxLayout()
        self.total_chapters_label = QLabel("总章节: 0")
        self.total_words_label = QLabel("总字数: 0")
        
        stats_layout.addWidget(self.total_chapters_label)
        stats_layout.addWidget(self.total_words_label)
        stats_layout.addStretch()
        
        layout.addLayout(stats_layout)
        
        return panel
        
    def create_chapter_detail_panel(self) -> QWidget:
        """创建章节详情面板"""
        panel = UIUtils.create_card_widget("章节详情")
        layout = panel.layout()
        
        # 章节基本信息
        info_layout = QFormLayout()
        
        self.chapter_title_input = UIUtils.create_material_input("章节标题")
        info_layout.addRow("章节标题:", self.chapter_title_input)
        
        self.chapter_number_input = UIUtils.create_material_input("章节序号")
        info_layout.addRow("章节序号:", self.chapter_number_input)
        
        self.target_words_input = UIUtils.create_material_input("目标字数")
        info_layout.addRow("目标字数:", self.target_words_input)
        
        self.chapter_status_combo = UIUtils.create_material_combobox([
            "未开始", "大纲中", "写作中", "初稿完成", "修改中", "已完成"
        ])
        info_layout.addRow("写作状态:", self.chapter_status_combo)
        
        layout.addLayout(info_layout)
        
        # 章节大纲
        outline_label = QLabel("章节大纲:")
        outline_label.setFont(self.ui_config.get_font("subtitle"))
        layout.addWidget(outline_label)
        
        self.chapter_outline_text = UIUtils.create_material_textarea(
            "请输入章节的详细大纲..."
        )
        self.chapter_outline_text.setMinimumHeight(150)
        layout.addWidget(self.chapter_outline_text)
        
        # 关键情节点
        plot_points_label = QLabel("关键情节点:")
        plot_points_label.setFont(self.ui_config.get_font("subtitle"))
        layout.addWidget(plot_points_label)
        
        self.plot_points_text = UIUtils.create_material_textarea(
            "列出本章的关键情节点..."
        )
        self.plot_points_text.setMinimumHeight(100)
        layout.addWidget(self.plot_points_text)
        
        # 人物出场
        characters_label = QLabel("出场人物:")
        characters_label.setFont(self.ui_config.get_font("subtitle"))
        layout.addWidget(characters_label)
        
        self.characters_text = UIUtils.create_material_textarea(
            "列出本章出场的主要人物..."
        )
        self.characters_text.setMinimumHeight(80)
        layout.addWidget(self.characters_text)
        
        # 备注
        notes_label = QLabel("备注:")
        notes_label.setFont(self.ui_config.get_font("subtitle"))
        layout.addWidget(notes_label)
        
        self.notes_text = UIUtils.create_material_textarea(
            "其他备注信息..."
        )
        self.notes_text.setMinimumHeight(60)
        layout.addWidget(self.notes_text)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.save_chapter_btn = UIUtils.create_material_button("💾 保存章节", button_type="primary")
        self.save_chapter_btn.clicked.connect(self.save_chapter)
        button_layout.addWidget(self.save_chapter_btn)
        
        self.generate_content_btn = UIUtils.create_material_button("🤖 生成内容", button_type="secondary")
        self.generate_content_btn.clicked.connect(self.generate_chapter_content)
        button_layout.addWidget(self.generate_content_btn)
        
        self.delete_chapter_btn = UIUtils.create_material_button("🗑️ 删除", button_type="outline")
        self.delete_chapter_btn.clicked.connect(self.delete_chapter)
        button_layout.addWidget(self.delete_chapter_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        return panel
        
    def add_volume(self):
        """添加卷"""
        dialog = VolumeDialog(self)
        if dialog.exec() == QDialog.Accepted:
            volume_data = dialog.get_data()
            self.add_volume_to_tree(volume_data)
            self.logger.info(f"添加卷: {volume_data['title']}")
            
    def add_chapter(self):
        """添加章节"""
        # 获取当前选中的卷
        current_item = self.outline_tree.currentItem()
        if current_item and current_item.parent() is None:
            # 选中的是卷
            volume_item = current_item
        else:
            # 没有选中卷，提示用户
            UIUtils.show_message(self, "提示", "请先选择要添加章节的卷", "info")
            return
            
        dialog = ChapterDialog(self)
        if dialog.exec() == QDialog.Accepted:
            chapter_data = dialog.get_data()
            self.add_chapter_to_tree(volume_item, chapter_data)
            self.logger.info(f"添加章节: {chapter_data['title']}")
            
    def add_volume_to_tree(self, volume_data: Dict[str, Any]):
        """添加卷到树中"""
        volume_item = QTreeWidgetItem([volume_data['title'], "", ""])
        volume_item.setFont(0, self.ui_config.get_font("subtitle"))
        volume_item.setData(0, Qt.UserRole, {"type": "volume", "data": volume_data})
        
        self.outline_tree.addTopLevelItem(volume_item)
        self.outline_tree.expandItem(volume_item)
        
    def add_chapter_to_tree(self, volume_item: QTreeWidgetItem, chapter_data: Dict[str, Any]):
        """添加章节到树中"""
        chapter_item = QTreeWidgetItem([
            chapter_data['title'],
            f"{chapter_data.get('target_words', 0)}字",
            chapter_data.get('status', '未开始')
        ])
        chapter_item.setData(0, Qt.UserRole, {"type": "chapter", "data": chapter_data})
        
        volume_item.addChild(chapter_item)
        self.update_statistics()
        
    def on_chapter_selected(self, item: QTreeWidgetItem, column: int):
        """章节选中事件"""
        item_data = item.data(0, Qt.UserRole)
        if item_data and item_data["type"] == "chapter":
            self.current_chapter = item
            self.load_chapter_details(item_data["data"])
            
    def load_chapter_details(self, chapter_data: Dict[str, Any]):
        """加载章节详情"""
        self.chapter_title_input.setText(chapter_data.get('title', ''))
        self.chapter_number_input.setText(str(chapter_data.get('number', '')))
        self.target_words_input.setText(str(chapter_data.get('target_words', '')))
        
        # 设置状态
        status = chapter_data.get('status', '未开始')
        index = self.chapter_status_combo.findText(status)
        if index >= 0:
            self.chapter_status_combo.setCurrentIndex(index)
            
        self.chapter_outline_text.setPlainText(chapter_data.get('outline', ''))
        self.plot_points_text.setPlainText(chapter_data.get('plot_points', ''))
        self.characters_text.setPlainText(chapter_data.get('characters', ''))
        self.notes_text.setPlainText(chapter_data.get('notes', ''))
        
    def save_chapter(self):
        """保存章节"""
        if not self.current_chapter:
            UIUtils.show_message(self, "提示", "请先选择要保存的章节", "info")
            return
            
        # 收集章节数据
        chapter_data = {
            'title': self.chapter_title_input.text(),
            'number': self.chapter_number_input.text(),
            'target_words': self.target_words_input.text(),
            'status': self.chapter_status_combo.currentText(),
            'outline': self.chapter_outline_text.toPlainText(),
            'plot_points': self.plot_points_text.toPlainText(),
            'characters': self.characters_text.toPlainText(),
            'notes': self.notes_text.toPlainText()
        }
        
        # 更新树项数据
        self.current_chapter.setData(0, Qt.UserRole, {"type": "chapter", "data": chapter_data})
        
        # 更新显示
        self.current_chapter.setText(0, chapter_data['title'])
        self.current_chapter.setText(1, f"{chapter_data.get('target_words', 0)}字")
        self.current_chapter.setText(2, chapter_data['status'])
        
        self.update_statistics()
        self.logger.info(f"保存章节: {chapter_data['title']}")
        
    def generate_chapter_content(self):
        """生成章节内容"""
        if not self.current_chapter:
            UIUtils.show_message(self, "提示", "请先选择要生成内容的章节", "info")
            return
            
        self.logger.info("生成章节内容")
        # TODO: 实现AI生成章节内容
        
    def delete_chapter(self):
        """删除章节"""
        if not self.current_chapter:
            UIUtils.show_message(self, "提示", "请先选择要删除的章节", "info")
            return
            
        reply = UIUtils.show_message(self, "确认删除", "确定要删除这个章节吗？", "question")
        if reply == QMessageBox.Yes:
            parent = self.current_chapter.parent()
            if parent:
                parent.removeChild(self.current_chapter)
            else:
                index = self.outline_tree.indexOfTopLevelItem(self.current_chapter)
                self.outline_tree.takeTopLevelItem(index)
                
            self.current_chapter = None
            self.clear_chapter_details()
            self.update_statistics()
            self.logger.info("删除章节")
            
    def clear_chapter_details(self):
        """清空章节详情"""
        self.chapter_title_input.clear()
        self.chapter_number_input.clear()
        self.target_words_input.clear()
        self.chapter_status_combo.setCurrentIndex(0)
        self.chapter_outline_text.clear()
        self.plot_points_text.clear()
        self.characters_text.clear()
        self.notes_text.clear()
        
    def filter_chapters(self, text: str):
        """过滤章节"""
        # TODO: 实现章节搜索过滤
        pass
        
    def show_context_menu(self, position: QPoint):
        """显示右键菜单"""
        item = self.outline_tree.itemAt(position)
        if not item:
            return
            
        menu = QMenu(self)
        
        item_data = item.data(0, Qt.UserRole)
        if item_data:
            if item_data["type"] == "volume":
                add_chapter_action = menu.addAction("添加章节")
                add_chapter_action.triggered.connect(self.add_chapter)
                
                rename_action = menu.addAction("重命名卷")
                rename_action.triggered.connect(lambda: self.rename_item(item))
                
                delete_action = menu.addAction("删除卷")
                delete_action.triggered.connect(lambda: self.delete_volume(item))
                
            elif item_data["type"] == "chapter":
                edit_action = menu.addAction("编辑章节")
                edit_action.triggered.connect(lambda: self.on_chapter_selected(item, 0))
                
                duplicate_action = menu.addAction("复制章节")
                duplicate_action.triggered.connect(lambda: self.duplicate_chapter(item))
                
                delete_action = menu.addAction("删除章节")
                delete_action.triggered.connect(lambda: self.delete_chapter_item(item))
                
        menu.exec(self.outline_tree.mapToGlobal(position))
        
    def update_statistics(self):
        """更新统计信息"""
        total_chapters = 0
        total_words = 0
        
        for i in range(self.outline_tree.topLevelItemCount()):
            volume_item = self.outline_tree.topLevelItem(i)
            for j in range(volume_item.childCount()):
                chapter_item = volume_item.child(j)
                total_chapters += 1
                
                item_data = chapter_item.data(0, Qt.UserRole)
                if item_data and item_data["type"] == "chapter":
                    chapter_data = item_data["data"]
                    words = chapter_data.get('target_words', 0)
                    if isinstance(words, str) and words.isdigit():
                        total_words += int(words)
                    elif isinstance(words, int):
                        total_words += words
                        
        self.total_chapters_label.setText(f"总章节: {total_chapters}")
        self.total_words_label.setText(f"总字数: {total_words:,}")

class VolumeDialog(QDialog):
    """卷添加对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("添加卷")
        self.setModal(True)
        self.resize(400, 200)
        
        layout = QVBoxLayout(self)
        
        # 卷标题
        form_layout = QFormLayout()
        self.title_input = UIUtils.create_material_input("请输入卷标题")
        form_layout.addRow("卷标题:", self.title_input)
        
        self.description_text = UIUtils.create_material_textarea("卷的描述...")
        self.description_text.setMaximumHeight(80)
        form_layout.addRow("描述:", self.description_text)
        
        layout.addLayout(form_layout)
        
        # 按钮
        button_layout = QHBoxLayout()
        self.ok_btn = UIUtils.create_material_button("确定", button_type="primary")
        self.ok_btn.clicked.connect(self.accept)
        
        self.cancel_btn = UIUtils.create_material_button("取消", button_type="outline")
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.ok_btn)
        button_layout.addWidget(self.cancel_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
    def get_data(self) -> Dict[str, Any]:
        """获取数据"""
        return {
            'title': self.title_input.text(),
            'description': self.description_text.toPlainText()
        }

class ChapterDialog(QDialog):
    """章节添加对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("添加章节")
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 章节信息
        form_layout = QFormLayout()
        
        self.title_input = UIUtils.create_material_input("请输入章节标题")
        form_layout.addRow("章节标题:", self.title_input)
        
        self.number_input = UIUtils.create_material_input("章节序号")
        form_layout.addRow("章节序号:", self.number_input)
        
        self.target_words_input = UIUtils.create_material_input("目标字数")
        form_layout.addRow("目标字数:", self.target_words_input)
        
        layout.addLayout(form_layout)
        
        # 章节大纲
        outline_label = QLabel("章节大纲:")
        layout.addWidget(outline_label)
        
        self.outline_text = UIUtils.create_material_textarea("请输入章节大纲...")
        layout.addWidget(self.outline_text)
        
        # 按钮
        button_layout = QHBoxLayout()
        self.ok_btn = UIUtils.create_material_button("确定", button_type="primary")
        self.ok_btn.clicked.connect(self.accept)
        
        self.cancel_btn = UIUtils.create_material_button("取消", button_type="outline")
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.ok_btn)
        button_layout.addWidget(self.cancel_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
    def get_data(self) -> Dict[str, Any]:
        """获取数据"""
        return {
            'title': self.title_input.text(),
            'number': self.number_input.text(),
            'target_words': self.target_words_input.text(),
            'status': '未开始',
            'outline': self.outline_text.toPlainText()
        }
